{"name": "pos-asha-backoffice", "version": "1.0.0", "description": "", "author": "", "license": "", "private": true, "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "test:watch": "ng test --watch"}, "dependencies": {"@angular/animations": "17.3.8", "@angular/cdk": "17.3.8", "@angular/common": "17.3.8", "@angular/compiler": "17.3.8", "@angular/core": "17.3.8", "@angular/forms": "17.3.8", "@angular/material": "17.3.8", "@angular/material-luxon-adapter": "17.3.8", "@angular/platform-browser": "17.3.8", "@angular/platform-browser-dynamic": "17.3.8", "@angular/router": "17.3.8", "@iplab/ngx-file-upload": "^17.1.0", "@ngneat/transloco": "6.0.0", "angular-datatables": "17.0.0", "apexcharts": "3.44.0", "crypto-js": "3.3.0", "datatables.net": "1.11.3", "datatables.net-dt": "1.11.3", "datatables.net-select": "1.3.3", "datatables.net-select-dt": "1.3.3", "highlight.js": "11.9.0", "jquery": "3.6.0", "lodash-es": "4.17.21", "luxon": "3.4.4", "ng-apexcharts": "1.8.0", "ngx-awesome-uploader": "17.1.0", "ngx-color-picker": "^17.0.0", "ngx-drag-drop": "^18.0.2", "ngx-mask": "17.0.4", "ngx-mat-select-search": "^7.0.6", "ngx-quill": "24.0.2", "ngx-toastr": "18.0.0", "perfect-scrollbar": "1.5.5", "quill": "1.3.7", "rxjs": "7.8.1", "tslib": "2.6.2", "zone.js": "0.14.2"}, "devDependencies": {"@angular-builders/jest": "17.0.3", "@angular-devkit/build-angular": "17.3.7", "@angular/cli": "17.3.7", "@angular/compiler-cli": "17.3.8", "@tailwindcss/typography": "0.5.10", "@types/chroma-js": "2.4.3", "@types/crypto-js": "3.1.47", "@types/datatables.net": "1.10.21", "@types/datatables.net-select": "1.2.10", "@types/highlight.js": "10.1.0", "@types/jest": "29.5.12", "@types/jquery": "3.5.9", "@types/lodash": "4.14.201", "@types/lodash-es": "4.17.11", "@types/luxon": "3.3.4", "@types/node": "20.12.10", "autoprefixer": "10.4.16", "chroma-js": "2.4.2", "jasmine-core": "5.1.1", "jest": "29.7.0", "lodash": "4.17.21", "postcss": "8.4.31", "tailwindcss": "3.3.5", "typescript": "5.2.2"}}