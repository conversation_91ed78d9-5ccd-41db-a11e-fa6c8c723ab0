pipeline {
    agent any

    environment {
        DISCORD_WEBHOOK_URL = 'https://discord.com/api/webhooks/1204375617710071899/mJoYhxvtHK0KOokOMKRHiz_gI3XuhjfmfPmdQ2kXzc5uKxZCINAKjqao8x2qrpzdZWMq'
    }

    stages {
        stage('Checkout Code') {
            steps {
                echo '📦 Checking out the code...'
                checkout scm
            }
        }

        stage('Build Angular in Docker') {
            steps {
                echo '🐳 Building Angular app inside Docker container with limited CPU/RAM...'
                sh '''
                    sudo docker run --rm \
                        --cpus="2" \
                        --memory="3g" \
                        -v "$PWD":/app \
                        -w /app \
                        node:20 \
                        bash -c "corepack enable && corepack prepare pnpm@latest --activate && pnpm install && pnpm run build --configuration=production"
                '''
            }
        }
    }

    post {
        success {
            echo '✅ Build succeeded!'
            sendDiscordNotification("✅ *${env.JOB_NAME}* - Build #${env.BUILD_NUMBER} succeeded! <${env.BUILD_URL}|View Logs>")
        }

        failure {
            echo '❌ Build failed!'
            sendDiscordNotification("❌ *${env.JOB_NAME}* - Build #${env.BUILD_NUMBER} failed! <${env.BUILD_URL}|View Logs>")
        }
    }
}

// ฟังก์ชันส่ง Discord Webhook
def sendDiscordNotification(message) {
    def payload = [content: message]
    def json = groovy.json.JsonOutput.toJson(payload)
    sh """curl -X POST -H 'Content-Type: application/json' -d '${json}' '${env.DISCORD_WEBHOOK_URL}'"""
}