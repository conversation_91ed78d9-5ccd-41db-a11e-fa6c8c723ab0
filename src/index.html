<!doctype html>
<html lang="en">
    <head>
        <title>UPOS</title>
        <meta charset="utf-8">
        <!-- <meta
            name="description"
            content="Admin Template and Starter Kit with Angular, Angular Material Components and TailwindCSS"> -->
        <!-- <meta
            name="keywords"
            content="Fuse,HTML,CSS,Angular,Angular 13,Angular 14,Angular 15,Material,Angular Components,Tailwind,Tailwind CSS,TailwindCSS,Admin Template,Admin Starter Kit"> -->
        <meta
            name="viewport"
            content="width=device-width, height=device-height, initial-scale=1.0, minimum-scale=1.0">

        <base href="/">

        <!-- Favicon -->
        <link
            rel="icon"
            type="image/png"
            href="favicon-16x16.png">
        <link
            rel="icon"
            type="image/png"
            href="favicon-32x32.png">

        <!-- Fonts -->
        <link
            href="assets/fonts/inter/inter.css"
            rel="stylesheet">

        <link
            href="https://fonts.gstatic.com"
            rel="preconnect">
        <link
            href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:ital,wght@0,400;0,500;0,600;1,400&display=swap"
            rel="stylesheet">

        <!-- Icon fonts -->
        <link
            href="https://fonts.googleapis.com/icon?family=Material+Icons"
            rel="stylesheet">

        <!-- Splash screen styles -->
        <link
            href="assets/styles/splash-screen.css"
            rel="stylesheet">

        <!-- Cropper -->

        <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.4.3/cropper.min.js" async>  </script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.4.3/cropper.css"  />


    </head>

    <body>

        <!-- Splash screen -->
        <fuse-splash-screen>
            <img
                src="assets/images/logo/Logo_White.svg" class="w-48"
                alt="Fuse logo">
            <div class="spinner">
                <div class="bounce1"></div>
                <div class="bounce2"></div>
                <div class="bounce3"></div>
            </div>
        </fuse-splash-screen>

        <!-- App root -->
        <app-root></app-root>

    </body>

</html>
