import { CommonModule } from '@angular/common';
import { Component, EventEmitter, forwardRef, Input, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { ControlValueAccessor, FormControl, FormsModule, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { BehaviorSubject, debounceTime, takeUntil, Subject, ReplaySubject, startWith, distinctUntilChanged } from 'rxjs';

export interface DropdownSearchConfig {
  valueKey: string;        // key สำหรับ value ที่ต้องการ return (เช่น 'id')
  displayKeys: string[];   // keys สำหรับแสดงผล (เช่น ['firstname', 'lastname'])
  searchKeys: string[];    // keys สำหรับใช้ในการค้นหา (เช่น ['firstname', 'lastname', 'email'])
  displaySeparator?: string; // ตัวคั่นสำหรับแสดงผล (default: ' ')
}

@Component({
  selector: 'app-dropdown-search-lazy',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatOptionModule,
    ReactiveFormsModule,
    NgxMatSelectSearchModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DropdownSearchLazyComponent),
      multi: true
    }
  ],
  template: `
    <mat-form-field [class]="fieldClass">
      @if (label) {
        <mat-label class="font-bold">{{ label }}</mat-label>
      }
      <mat-select
        class="w-full"
        [value]="selectedItem"
        (selectionChange)="onSelectOption($event.value)"
        (blur)="onBlur()"
        (focus)="onFocus()"
        [placeholder]="placeholder"
        [required]="required"
        [disabled]="readonly"
      >
        <mat-option>
          <ngx-mat-select-search
            [formControl]="searchControl"
            noEntriesFoundLabel="ไม่พบ"
            placeholderLabel="พิมพ์เพื่อค้นหา..."
            [clearSearchInput]="false"
            [hideClearSearchButton]="true">
          </ngx-mat-select-search>
        </mat-option>
        
        @if ((filteredItems | async)?.length === 0) {
          <mat-option disabled>
            <span class="text-gray-500">ไม่พบข้อมูล</span>
          </mat-option>
        }
        
        <mat-option
          *ngFor="let item of filteredItems | async"
          [value]="item">
          {{ getDisplayText(item) }}
        </mat-option>
      </mat-select>

      <!-- Clear button -->
      @if (showClearButton && selectedItem && !readonly) {
        <button 
          matSuffix 
          mat-icon-button 
          type="button"
          (click)="clearSelection()"
          [attr.aria-label]="'Clear selection'">
          <mat-icon>close</mat-icon>
        </button>
      }
    </mat-form-field>
  `,
  styles: ``
})
export class DropdownSearchLazyComponent<T = any> implements OnInit, OnDestroy, ControlValueAccessor {
  @Input() items: T[] = [];
  @Input() fieldClass: string = 'w-full';
  @Input() label = '';
  @Input() placeholder = '';
  @Input() debounceTime: number = 300;
  @Input() required: boolean = false;
  @Input() readonly: boolean = false;
  @Input() showClearButton: boolean = true;

  // Configuration สำหรับกำหนดวิธีการแสดงผลและค้นหา
  @Input() config!: DropdownSearchConfig;

  @Output() selected = new EventEmitter<T>();

  searchControl = new FormControl('');
  filteredItems = new ReplaySubject<T[]>(1);

  private destroy$ = new Subject<void>();
  selectedItem: T | null = null; // เปลี่ยนจาก private เป็น public

  private itemsArray: T[] = [];

  constructor() { }

  ngOnInit() {
    this.validateConfig();
    this.itemsArray = this.items || [];
    this.filteredItems.next(this.itemsArray);
    this.setupFiltering();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['items']) {
      this.itemsArray = this.items || [];
      this.filteredItems.next(this.itemsArray);

      // ถ้ามี selectedItem แล้วแต่ไม่มีใน items ใหม่ ให้ clear
      if (this.selectedItem && !this.itemsArray.some(item =>
        this.getNestedValue(item, this.config.valueKey) ===
        this.getNestedValue(this.selectedItem, this.config.valueKey)
      )) {
        this.clearSelection();
      }
    }
  }

  writeValue(value: any): void {
    if (value !== undefined && value !== null && this.itemsArray.length > 0) {
      const matchedItem = this.itemsArray.find(i =>
        this.getNestedValue(i, this.config.valueKey) === value
      );
      if (matchedItem) {
        this.selectedItem = matchedItem;
        this.filterItems(''); // Reset filter to show all items
      } else {
        this.selectedItem = null;
      }
    } else {
      this.selectedItem = null;
    }

    // Reset search control
    this.searchControl.setValue('', { emitEvent: false });
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    // Handle disabled state
    if (isDisabled) {
      this.searchControl.disable();
    } else {
      this.searchControl.enable();
    }
  }

  onChange = (_: any) => { };
  onTouched = () => { };

  clearSelection(): void {
    this.selectedItem = null;
    this.searchControl.setValue('', { emitEvent: false });
    this.onChange(null);
    this.filteredItems.next(this.itemsArray);
    this.onTouched();
  }

  private validateConfig(): void {
    if (!this.config) {
      throw new Error('DropdownSearchLazyComponent: config is required');
    }
    if (!this.config.valueKey) {
      throw new Error('DropdownSearchLazyComponent: config.valueKey is required');
    }
    if (!this.config.displayKeys || this.config.displayKeys.length === 0) {
      throw new Error('DropdownSearchLazyComponent: config.displayKeys is required');
    }
    if (!this.config.searchKeys || this.config.searchKeys.length === 0) {
      throw new Error('DropdownSearchLazyComponent: config.searchKeys is required');
    }

    // Set default separator
    if (!this.config.displaySeparator) {
      this.config.displaySeparator = ' ';
    }
  }

  private setupFiltering(): void {
    this.searchControl.valueChanges.pipe(
      startWith(''),
      debounceTime(this.debounceTime),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(value => {
      // ถ้าเป็น object (selected item) ไม่ต้อง filter
      if (typeof value === 'object' && value !== null) {
        return;
      }
      this.filterItems(value || '');
    });

    // Initial load
    this.filteredItems.next(this.itemsArray);
  }

  // Helper method สำหรับเข้าถึง nested property
  private getNestedValue(obj: any, path: string): any {
    if (!obj || !path) return null;
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  getDisplayText = (item: T): string => {
    if (!item || !this.config) return '';

    const displayParts = this.config.displayKeys.map(key => {
      const value = this.getNestedValue(item, key);
      return value ? value.toString() : '';
    }).filter(part => part);

    return displayParts.join(this.config.displaySeparator || ' ');
  }

  private filterItems(searchValue: string): void {
    const filterValue = searchValue.toLowerCase().trim();

    let itemsToShow = filterValue
      ? this.itemsArray.filter(item =>
        this.config.searchKeys.some(key => {
          const value = this.getNestedValue(item, key);
          return value?.toString().toLowerCase().includes(filterValue);
        })
      )
      : this.itemsArray;

    // เรียงลำดับให้ item ที่เลือกอยู่ขึ้นมาก่อน
    if (this.selectedItem) {
      itemsToShow = itemsToShow.filter(item => item !== this.selectedItem);
      this.filteredItems.next([this.selectedItem, ...itemsToShow]);
    } else {
      this.filteredItems.next(itemsToShow);
    }
  }

  onSelectOption(selectedItem: T) {
    if (!selectedItem) {
      console.log('No item selected');
      return;
    }

    this.selectedItem = selectedItem;

    // Clear search to show selected item text
    this.searchControl.setValue('', { emitEvent: false });

    // Emit value change
    const value = this.getNestedValue(selectedItem, this.config.valueKey);
    this.onChange(value);

    this.selected.emit(selectedItem);
  }

  onFocus(): void {
    // Reset filter when focused เพื่อแสดงทุก options
    this.filterItems('');
  }

  onBlur(): void {
    this.onTouched();
  }
}