<file-upload [control]="control" class="min-h-50 h-auto w-96 p-5">
  <ng-template let-isFileDragDropAvailable="isFileDragDropAvailable" #placeholder>
    @if (control.size === 0) {
    <div class="flex flex-row items-center gap-3">
      <svg viewBox="0 0 512 512">
        <path
          d="M296 384h-80c-13.3 0-24-10.7-24-24V192h-87.7c-17.8 0-26.7-21.5-14.1-34.1L242.3 5.7c7.5-7.5 19.8-7.5 27.3 0l152.2 152.2c12.6 12.6 3.7 34.1-14.1 34.1H320v168c0 13.3-10.7 24-24 24zm216-8v112c0 13.3-10.7 24-24 24H24c-13.3 0-24-10.7-24-24V376c0-13.3 10.7-24 24-24h136v8c0 30.9 25.1 56 56 56h80c30.9 0 56-25.1 56-56v-8h136c13.3 0 24 10.7 24 24zm-124 88c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20zm64 0c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20z">
        </path>
      </svg>
      <div class="overflow-hidden w-auto text-base">
        @if (isFileDragDropAvailable) {
        <b>Drag and drop</b> files<br> or click here
        } @else {
        <b>Click here</b> to<br> choose a files
        }
      </div>
    </div>
    }
  </ng-template>

  <ng-template let-i="index" let-file="file" let-control="control" #item>
    <div class="relative cursor-pointer" (click)="control.click()">
      @if (control.valid) {
      <img class="max-w-full" [src]="uploadedFile | async">
      }
    </div>
  </ng-template>
</file-upload>
