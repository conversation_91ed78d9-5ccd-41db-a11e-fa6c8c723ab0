import { Pipe, PipeTransform } from '@angular/core';
import { DateObjectUnits, DateTime } from 'luxon';

@Pipe({
  name: 'thaidate',
  standalone: true
})
export class ThaiDatePipe implements PipeTransform {

  transform(value: any, format: string = 'dd MMMM yyyy', locale: string = 'th-TH'): string {
    if (!value) return '';

    let dt: DateTime;

    // ตรวจสอบและแปลงให้กลายเป็น Luxon DateTime
    if (DateTime.isDateTime(value)) {
      dt = value;
    } else if (value instanceof Date) {
      dt = DateTime.fromJSDate(value);
    } else if (typeof value === 'string') {
      dt = DateTime.fromISO(value, { setZone: true });
      if (!dt.isValid) dt = DateTime.fromRFC2822(value);
      if (!dt.isValid) dt = DateTime.fromSQL(value);
    } else if (typeof value === 'object' && 'year' in value) {
      dt = DateTime.fromObject(value as DateObjectUnits);
    } else {
      return '';
    }

    if (!dt.isValid) return '';

    // บังคับให้ใช้ Asia/Bangkok timezone เสมอ
    dt = dt.setZone('Asia/Bangkok');

    // ตรวจสอบว่า DateTime ยังคงถูกต้องหลังจากการแปลง timezone
    if (!dt.isValid) {
      console.warn('DateTime became invalid after timezone conversion');
      return '';
    }
    
    // เพิ่มปีเป็นพุทธศักราช
    const buddhistYear = dt.year + 543;
    const formatted = dt.set({ year: buddhistYear }).setLocale(locale).toFormat(format);

    return formatted;
  }

}
