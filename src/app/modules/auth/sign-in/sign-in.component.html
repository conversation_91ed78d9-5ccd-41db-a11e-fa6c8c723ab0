<div class="min-h-screen flex items-center justify-center p-4 bg-white bg-cover bg-center w-full"
    style="background-image: url('/assets/images/bg-login.png');">
    <div class="w-full max-w-4xl shadow-lg overflow-hidden bg-slate-400/95 rounded-2xl flex flex-col md:flex-row">
        <!-- Logo Section - Hidden on mobile, visible on tablet/desktop -->
        <div class="hidden md:flex flex-1 items-center justify-center p-8 bg-gradient-to-br from-blue-500 to-blue-700">
            <div class="max-w-xs w-full">
                <img src="assets/images/logo/logo-pos.png" alt="Company Logo" class="w-full h-auto object-contain" />
            </div>
        </div>

        <!-- Form Section -->
        <div class="w-full flex-1 p-6 sm:p-8 md:p-12 lg:p-16">
            <div class="max-w-md mx-auto">
                <!-- Title -->
                <div class="text-center mb-8">
                    <h1 class="text-3xl sm:text-4xl font-bold text-gray-900">ยินดีต้อนรับเข้าสู่ระบบ</h1>
                    <p class="mt-2 text-sm sm:text-base text-gray-600">กรุณาลงชื่อเพื่อเข้าสู่ระบบ</p>
                </div>

                <!-- Alert -->
                <fuse-alert class="mt-4 mb-6" *ngIf="showAlert" [appearance]="'outline'" [showIcon]="false"
                    [type]="alert.type" [@shake]="alert.type === 'error'">
                    {{alert.message}}
                </fuse-alert>

                <!-- Sign in form -->
                <form class="space-y-6" [formGroup]="signInForm" #signInNgForm="ngForm">
                    <!-- Username field -->
                    <mat-form-field class="w-full">
                        <mat-label>Username</mat-label>
                        <input id="username" matInput [formControlName]="'username'">
                        <mat-error *ngIf="signInForm.get('username').hasError('required')">
                            <span class="text-red-600">**กรุณากรอกชื่อผู้ใช้งาน**</span>
                        </mat-error>
                    </mat-form-field>

                    <!-- Password field -->
                    <mat-form-field class="w-full">
                        <mat-label>Password</mat-label>
                        <input id="password" matInput type="password" [formControlName]="'password'" #passwordField>
                        <button mat-icon-button type="button"
                            (click)="passwordField.type === 'password' ? passwordField.type = 'text' : passwordField.type = 'password'"
                            matSuffix>
                            <mat-icon class="icon-size-5" *ngIf="passwordField.type === 'password'"
                                [svgIcon]="'heroicons_solid:eye'"></mat-icon>
                            <mat-icon class="icon-size-5" *ngIf="passwordField.type === 'text'"
                                [svgIcon]="'heroicons_solid:eye-off'"></mat-icon>
                        </button>
                        <mat-error>
                            <span class="text-red-600">**กรุณากรอกรหัสผ่าน**</span>
                        </mat-error>
                    </mat-form-field>

                    <!-- Submit button -->
                    <button
                        class="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        color="primary" mat-flat-button [disabled]="signInForm.disabled" (click)="signIn()">
                        <div class="flex items-center justify-center">
                            <span *ngIf="!signInForm.disabled">เข้าสู่ระบบ</span>
                            <div *ngIf="signInForm.disabled" class="spinner-overlay"></div>
                        </div>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>