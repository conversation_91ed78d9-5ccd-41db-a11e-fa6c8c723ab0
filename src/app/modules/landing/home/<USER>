<div class="flex flex-col items-center justify-center w-full h-full">
    <div class="w-full max-w-3xl">
        <div class="max-w-none mx-auto prose prose-sm">
            <img
                class="w-20"
                src="assets/images/logo/logo.svg"
                alt="Logo image">
            <h1>Landing Module</h1>
            <p>
                This can be the landing or the welcome page of your application. If you have an SSR (Server Side Rendering) setup, or if you don't need to have Search engine
                visibility and optimizations, you can even use this page as your primary landing page.
            </p>
            <p>
                This is a separate "module", it has its own directory and routing setup and also it's completely separated from the actual application. This is also a simple example of
                a multiple applications setup that uses the same codebase. You can have different entry points and essentially have different applications within the same codebase.
            </p>
        </div>
        <div>
            <a
                class="mt-8"
                mat-flat-button
                [color]="'primary'"
                [routerLink]="'/dashboards/project'">
                <span>Launch the App</span>
                <mat-icon
                    class="ml-2 icon-size-5"
                    [svgIcon]="'heroicons_mini:arrow-long-right'"></mat-icon>
            </a>
        </div>
    </div>
</div>
