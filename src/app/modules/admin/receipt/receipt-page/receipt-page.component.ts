import { CommonModule } from '@angular/common';
import { Component, ViewChild } from '@angular/core';
import { ReactiveFormsModule, FormsModule, FormGroup, FormBuilder } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { Router, RouterLink } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { DataTablesModule, DataTableDirective } from 'angular-datatables';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { FilePickerModule } from 'ngx-awesome-uploader';
import { ToastrService } from 'ngx-toastr';
import { Subject } from 'rxjs';
import { DialogCreditComponent } from '../../member/dialog-credit/dialog-credit.component';
import { MemberComposeComponent } from '../../member/dialogcustomer/member-compose.component';
import { PictureComponent } from '../../picture/picture.component';
import { ProductComposeComponent } from '../../product/dialog/product-compose/product-compose.component';
import { DialogForm } from '../../user/form-dialog/dialog.component';
import { ReceiptService } from '../receipt.service';
import { ThaiDatePipe } from 'app/modules/common/pipe/thai-date.pipe';
import { ReceiptStatus } from '../receipt.type';

@Component({
  selector: 'asha-receipt-page',
  standalone: true,
  imports: [
    CommonModule,
    DataTablesModule,
    MatButtonModule,
    MatIconModule,
    FilePickerModule,
    MatMenuModule,
    MatDividerModule,
    MatSelectModule,
    MatInputModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    FormsModule,
    RouterLink,
    ThaiDatePipe
  ],
  providers: [
    ThaiDatePipe
  ],
  templateUrl: './receipt-page.component.html',
  styleUrl: './receipt-page.component.scss'
})
export class ReceiptPageComponent {
  dtOptions: any = {};
  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();

  form: FormGroup

  @ViewChild('textStatus') textStatus: any;
  @ViewChild('btNg') btNg: any;
  @ViewChild('btPicture') btPicture: any;
  @ViewChild('total') total: any;

  @ViewChild(DataTableDirective, { static: false })
  dtElement: DataTableDirective;

  constructor(
    private _service: ReceiptService,
    private fuseConfirmationService: FuseConfirmationService,
    private toastr: ToastrService,
    public dialog: MatDialog,
    private _router: Router,
    private _fb: FormBuilder,
    private thaiDatePipe: ThaiDatePipe
  ) {
  }
  ngOnInit(): void {
    setTimeout(() =>
      this.loadTable());

  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.dtTrigger.next(this.dtOptions);
    }, 200);
  }

  ngOnDestroy(): void {
    // Do not forget to unsubscribe the event
    this.dtTrigger.unsubscribe();
  }

  onChangeType() {
    this.rerender()
  }

  loadTable(): void {
    this.dtOptions = {
      pagingType: 'full_numbers',
      serverSide: true,     // Set the flag
      scrollX: true,
      ajax: (dataTablesParameters: any, callback) => {
        this._service?.datatable(dataTablesParameters).subscribe({
          next: (resp: any) => {
            callback({
              recordsTotal: resp?.meta?.totalItems,
              recordsFiltered: resp?.meta?.totalItems,
              data: resp?.data
            });
          }, error: () => {
            this.toastr.error('เกิดข้อผิดพลาด')
          }
        })
      },
      columns: [
        {
          title: 'ลำดับ',
          data: 'no',
          className: 'w-15 text-center'
        },
        {
          title: 'รหัส',
          data: 'receiptNo',
          className: 'w-30 text-center'
        },
        {
          title: 'วันที่รับสินค้า',
          data: 'receiptDate',
          className: 'w-30 text-center',
          render: (data) => {
            return this.thaiDatePipe.transform(data, 'dd/MM/yyyy', 'th-TH');
          }
        },
        {
          title: 'ผู้ขาย',
          data: 'vendor.name',
          className: 'w-30 text-left'
        },
        {
          title: 'ราคารวม',
          data: null,
          className: 'w-30 text-right',
          ngTemplateRef: {
            ref: this.total,
          },
        },
        {
          title: 'สถานะ',
          data: 'status',
          className: 'w-30 text-center',
          render: (data: any) => {
            const statusText = ReceiptStatus[data].text;
            const statusClass = ReceiptStatus[data].class;

            return `<span class="rounded-lg border px-3 py-1 font-medium inline-block text-sm bg-white ${statusClass}">${statusText}</span>`;
          }
        },
        {
          title: 'วันที่สร้าง',
          data: 'createdAt',
          className: 'w-30 text-center',
          render: (data) => {
            return this.thaiDatePipe.transform(data, 'dd/MM/yyyy HH:mm:ss', 'th-TH');
          }
        },
        {
          title: 'จัดการ',
          data: null,
          defaultContent: '',
          ngTemplateRef: {
            ref: this.btNg,
          },
          className: 'w-15 text-center',
        }
      ]
    }
  }

  rerender(): void {
    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
      // Destroy the table first
      dtInstance.destroy();
      // Call the dtTrigger to rerender again
      this.dtTrigger.next(this.dtOptions);
    });
  }

  opendialogapro2() {
    const DialogRef = this.dialog.open(DialogForm, {
      disableClose: true,
      width: '500px',
      height: 'auto',
      enterAnimationDuration: 300,
      exitAnimationDuration: 300,
      data: {
        type: 'NEW'
      }
    });
    DialogRef.afterClosed().subscribe((result) => {
      if (result) {
        console.log(result, 'result')
        this.rerender();
      }
    });
  }

  opendialogapro() {
    const DialogRef = this.dialog.open(MemberComposeComponent, {
      disableClose: true,
      width: '500px',
      height: 'auto',
      enterAnimationDuration: 300,
      exitAnimationDuration: 300,
      data: {
        type: 'NEW'
      }
    });
    DialogRef.afterClosed().subscribe((result) => {
      if (result) {
        console.log(result, 'result')
        this.rerender();
      }
    });
  }

  openDialogEdit(item: any) {
    const DialogRef = this.dialog.open(MemberComposeComponent, {
      disableClose: true,
      width: '500px',
      enterAnimationDuration: 300,
      exitAnimationDuration: 300,
      data: {
        type: 'EDIT',
        value: item
      }
    });
    DialogRef.afterClosed().subscribe((result) => {
      if (result) {
        console.log(result, 'result')
        this.rerender();
      }
    });
  }

  openDialoCredit(item: any) {
    const DialogRef = this.dialog.open(DialogCreditComponent, {
      disableClose: true,
      width: '500px',
      enterAnimationDuration: 300,
      exitAnimationDuration: 300,
      data: {
        type: 'EDIT',
        value: item
      }
    });
    DialogRef.afterClosed().subscribe((result) => {
      if (result) {
        console.log(result, 'result')
        this.rerender();
      }
    });
  }

  clickDelete(id: any) {
    const confirmation = this.fuseConfirmationService.open({
      title: "ยืนยันลบข้อมูล",
      message: "กรุณาตรวจสอบข้อมูล หากลบข้อมูลแล้วจะไม่สามารถนำกลับมาได้",
      icon: {
        show: true,
        name: "heroicons_outline:exclamation-triangle",
        color: "warn"
      },
      actions: {
        confirm: {
          show: true,
          label: "ยืนยัน",
          color: "primary"
        },
        cancel: {
          show: true,
          label: "ยกเลิก"
        }
      },
      dismissible: false
    })

    confirmation.afterClosed().subscribe(
      result => {
        if (result == 'confirmed') {
          // this._service.delete(id).subscribe({
          //   error: (err) => {

          //   },
          //   complete: () => {
          //     this.toastr.success('ดำเนินการลบสำเร็จ');
          //     this.rerender();
          //   },
          // });
        }
      }
    )
  }

  move_upGrade() {
    const confirmation = this.fuseConfirmationService.open({
      title: "ยืนยันการเลื่อนระดับชั้น",
      message: "กรุณาตรวจสอบข้อมูล หากกดยืนยันแล้วจะไม่สามารถแก้ไขได้",
      icon: {
        show: true,
        name: "heroicons_outline:exclamation-triangle",
        color: "warn"
      },
      actions: {
        confirm: {
          show: true,
          label: "ยืนยัน",
          color: "primary"
        },
        cancel: {
          show: true,
          label: "ยกเลิก"
        }
      },
      dismissible: false
    })

    confirmation.afterClosed().subscribe(
      result => {
        if (result == 'confirmed') {
          const confirmation = this.fuseConfirmationService.open({
            title: "ต้องการ backup ข้อมูลรึเปล่า",
            message: "เผื่อในกรณีที่ต้องการย้อนกลับหรือเกิดการผิดพลาด",
            icon: {
              show: true,
              name: "heroicons_outline:exclamation-triangle",
              color: "warn"
            },
            actions: {
              confirm: {
                show: true,
                label: "ยืนยัน",
                color: "primary"
              },
              cancel: {
                show: true,
                label: "ยกเลิก"
              }
            },
            dismissible: false
          })

          confirmation.afterClosed().subscribe(
            result => {
              if (result == 'confirmed') {
                // this._service.backUpData().subscribe({
                //   error: (err) => {
                //     this.toastr.error('เกิดข้อผิดพลาด');
                //   },
                //   complete: () => {
                //     this.toastr.success('download file backup complete');
                //     this._service.moveupGrade().subscribe({
                //       error: (err) => {
                //         this.toastr.error('เกิดข้อผิดพลาดในการเลื่อนระดับชั้น');
                //       },
                //       complete: () => {
                //         this.toastr.success('ดำเนินการเลื่อนระดับชั้นสำเร็จ');
                //         this.rerender();
                //       },
                //     });
                //   },
                // });
              }
            }
          )

        }
      }
    )
  }

  showPicture(imgObject: string): void {
    console.log(imgObject)
    this.dialog
      .open(PictureComponent, {
        autoFocus: false,
        data: {
          imgSelected: imgObject,
        },
      })
      .afterClosed()
      .subscribe(() => {
        // Go up twice because card routes are setup like this; "card/CARD_ID"
        // this._router.navigate(['./../..'], {relativeTo: this._activatedRoute});
      });
  }

  createProduct() {
    const DialogRef = this.dialog.open(ProductComposeComponent, {
      disableClose: true,
      width: '800px',
      height: '90%',
      enterAnimationDuration: 300,
      exitAnimationDuration: 300,
      data: {
        type: 'NEW'
      }
    });
    DialogRef.afterClosed().subscribe((result) => {
      if (result) {
        console.log(result, 'result')
        this.rerender();
      }
    });
  }

  approve(data: any, status: 'approve' | 'complete') {
    let message = 'ยืนยันการอนุมัติข้อมูล';

    if (status == 'complete') {
      message = 'ยืนยันการเสร็จสิ้นข้อมูล';
    }

    const confirmation = this.fuseConfirmationService.open({
      title: 'ยืนยันข้อมูล',
      message: message,
      icon: {
        show: true,
        name: "heroicons_outline:exclamation-triangle",
        color: "primary"
      },
      actions: {
        confirm: {
          show: true,
          label: "ยืนยัน",
          color: "primary"
        },
        cancel: {
          show: true,
          label: "ยกเลิก"
        }
      },
      dismissible: false
    })

    confirmation.afterClosed().subscribe(
      result => {
        if (result == 'confirmed') {
          let api = this._service.approve(data.id)
          if (status == 'complete') {
            api = this._service.complete(data.id)
          }

          api.subscribe({
            error: (err) => {
              this.toastr.error('เกิดข้อผิดพลาด');
            },
            complete: () => {
              this.toastr.success('ดำเนินการสำเร็จ');
              this.rerender();
            },
          });
        }
      }
    )
  }
}
