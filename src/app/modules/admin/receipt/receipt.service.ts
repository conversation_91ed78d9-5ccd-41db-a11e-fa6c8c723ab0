import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';
import { toUpper } from 'lodash';
import { BehaviorSubject, map, tap } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ReceiptService {

  private _products: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _vendors: BehaviorSubject<any[] | null> = new BehaviorSubject(null);

  get products$() {
    return this._products.asObservable();
  }

  get vendors$() {
    return this._vendors.asObservable();
  }

  constructor(
    private http: HttpClient,
  ) { }

  datatable(dataTablesParameters: any) {
    const { columns, order, search, start, length } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;

    return this.http.get('/api/receipts/datatables', {
      params: {
        page: page,
        limit: length,
        sortBy: sortBy,
        search: search.value,
      }
    }).pipe(
      map((resp: any) => {
        resp.data.forEach((e: any, i: number) => e.no = start + i + 1);
        return resp;
      })
    );
  }

  getProducts() {
    return this.http.get(environment.apiUrl + '/api/product')
      .pipe(
        tap((resp: any) => {
          this._products.next(resp);
        }),
      )
  }

  getVendors() {
    return this.http.get(environment.apiUrl + '/api/vendor')
      .pipe(
        tap((resp: any) => {
          this._vendors.next(resp);
        }),
      )
  }

  getById(id: number) {
    return this.http.get(environment.apiUrl + '/api/receipts/' + id)
  }

  createReceipt(data: any) {
    return this.http.post(environment.apiUrl + '/api/receipts', data)
  }

  updateReceipt(id: number, data: any) {
    return this.http.put(environment.apiUrl + '/api/receipts/' + id, data)
  }

  approve(id: number) {
    return this.http.post(environment.apiUrl + `/api/receipts/${id}/approve`, {})
  }

  complete(id: number) {
    return this.http.post(environment.apiUrl + `/api/receipts/${id}/complete`, {})
  }
}
