import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { DropdownSearchConfig, DropdownSearchLazyComponent } from 'app/modules/common/dropdown-search-lazy/dropdown-search-lazy.component';
import { ReceiptService } from '../receipt.service';
import { Observable } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { AuthService } from 'app/core/auth/auth.service';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'asha-receipt-create',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatDividerModule,
    DropdownSearchLazyComponent,
    MatDatepickerModule,
    MatIconModule
  ],
  templateUrl: './receipt-create.component.html',
  styleUrl: './receipt-create.component.scss'
})
export class ReceiptCreateComponent {
  id: number | null = null;

  form: FormGroup;

  vendorConfig: DropdownSearchConfig = {
    valueKey: 'id',
    displayKeys: ['code', 'name'],
    searchKeys: ['code', 'name'],
    displaySeparator: ' '
  };

  productConfig: DropdownSearchConfig = {
    valueKey: 'id',
    displayKeys: ['code', 'name'],
    searchKeys: ['code', 'name'],
    displaySeparator: ' '
  };

  products: Observable<any[]>;
  vendors: Observable<any[]>;

  constructor(
    private fb: FormBuilder,
    private receiptService: ReceiptService,
    private activatedRoute: ActivatedRoute,
    private toastr: ToastrService,
    private fuseConfirmationService: FuseConfirmationService,
    private router: Router,
    private authService: AuthService,
  ) {
    this.id = this.activatedRoute.snapshot.params.id;
  }

  ngOnInit(): void {
    this.form = this.fb.group({
      branchId: [this.authService.branchId, Validators.required],
      status: 'pending',
      receiptDate: [new Date()],
      receiptNo: [null],
      vendorId: [null, Validators.required],
      notes: [null],
      items: this.fb.array([])
    });

    this.products = this.receiptService.products$;
    this.vendors = this.receiptService.vendors$;

    if (this.id) {
      this.loadData()
    } else {
      this.addRow();
    }
  }

  addRow(data?: any) {
    const value = this.fb.group({
      productId: [null, Validators.required],
      quantity: [1, Validators.required],
      unitCost: [0, Validators.required],
      totalAmount: [0, Validators.required],
      notes: '',
    });

    if (data) {
      value.patchValue({
        productId: data?.product?.id,
        quantity: data.quantity,
        unitCost: data.unitCost,
        totalAmount: data.totalAmount,
        notes: data.notes,
      }, { emitEvent: false });
    }

    value.get('quantity').valueChanges.subscribe(
      (quantity) => {
        const unitCost = value.get('unitCost').value;
        const total = quantity * unitCost;
        value.get('totalAmount').setValue(total);
      }
    );

    value.get('unitCost').valueChanges.subscribe(
      (unitCost) => {
        const quantity = value.get('quantity').value;
        const total = quantity * unitCost;
        value.get('totalAmount').setValue(total);
      }
    );

    this.items.push(value);
  }

  removeRow(index: number) {
    this.items.removeAt(index);
  }

  get items() {
    return this.form.get('items') as FormArray;
  }

  submit() {
    if (this.form.invalid) {
      this.form.markAsTouched();
      this.form.markAsDirty();
      return;
    }

    const confirmation = this.fuseConfirmationService.open({
      title: "ยืนยันการบันทึกข้อมูล",
      icon: {
        show: true,
        name: "heroicons_outline:exclamation-triangle",
        color: "primary"
      },
      actions: {
        confirm: {
          show: true,
          label: "ยืนยัน",
          color: "primary"
        },
        cancel: {
          show: true,
          label: "ยกเลิก"
        }
      },
      dismissible: false
    })

    confirmation.afterClosed().subscribe(
      result => {
        if (result == 'confirmed') {
          if (this.id) {
            this.receiptService.updateReceipt(this.id, this.form.value).subscribe({
              error: (err) => {
                this.toastr.error(err?.error?.message ?? 'ไม่สามารถบันทึกข้อมูลได้')
              },
              complete: () => {
                this.toastr.success('ดำเนินการแก้ไขข้อมูลสำเร็จ')

                this.router.navigateByUrl('/receipt');
              },
            });
            
            return;
          }

          this.receiptService.createReceipt(this.form.value).subscribe({
            error: (err) => {
              this.toastr.error('ไม่สามารถบันทึกข้อมูลได้')
            },
            complete: () => {
              this.toastr.success('ดำเนินการเพิ่มข้อมูลสำเร็จ')

              this.router.navigateByUrl('/receipt');
            },
          });
        }
      }
    )
  }

  onProductSelected(event: any, control: FormGroup) {
    control.patchValue({
      unitCost: event.cost
    });
  }

  loadData() {
    this.receiptService.getById(this.id).subscribe((resp: any) => {
      this.form.patchValue({
        branchId: resp?.branch?.id,
        status: resp.status,
        receiptDate: resp.receiptDate,
        receiptNo: resp.receiptNo,
        vendorId: resp?.vendor?.id,
        notes: resp.notes,
      });

      this.items.clear();
      resp.items.forEach((item: any) => {
        this.addRow(item);
      });
    });
  }

  onClose() {
    this.router.navigateByUrl('/receipt');
  }
}
