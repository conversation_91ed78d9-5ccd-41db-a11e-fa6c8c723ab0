<div class="flex min-w-0 flex-auto flex-col">
  <div class="mx-auto flex w-full max-w-7xl flex-auto justify-center p-6 sm:p-8">

    <form [formGroup]="form" class="flex flex-col w-full bg-card p-6 rounded">
      <div class="flex flex-row justify-between pb-2">
        <h2 class="text-2xl font-extrabold leading-7 tracking-tight truncate md:text-xl sm:leading-10 md:px-6">
          {{ id ? 'แก้ไข' : 'เพิ่ม' }}ใบรับสินค้า
        </h2>
        <div class="flex gap-4">
          <button type="submit" mat-flat-button color="warn" (click)="onClose()">
            ยกเลิก
          </button>
          <button type="submit" mat-flat-button [color]="'primary'" (click)="submit()">
            บันทึก
          </button>
        </div>
      </div>

      <div class="flex w-full gap-4">
        <app-dropdown-search-lazy formControlName="vendorId" label="ผู้จำหน่าย" [items]="vendors | async"
          [config]="vendorConfig" class="w-1/2"></app-dropdown-search-lazy>

        <mat-form-field class="w-1/2">
          <mat-label>วันที่รับสินค้า</mat-label>
          <input matInput [matDatepicker]="picker" formControlName="receiptDate">
          <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>

      </div>

      <div class="flex items-center gap-4">
        <mat-label>รายการ</mat-label>
        <button mat-flat-button [color]="'primary'" (click)="addRow()">
          เพิ่ม
        </button>
      </div>

      <mat-divider class="my-4"> </mat-divider>

      <table class="w-full">
        <thead>
          <tr class="text-left">
            <th class="w-6/12">สินค้า</th>
            <th class="w-1/12">จำนวน</th>
            <th class="w-1/12">ราคาต่อหน่วย</th>
            <th class="w-1/12">ราคารวม</th>
            <th class="w-auto">หมายเหตุ</th>
            <th></th>
          </tr>
        </thead>
        <tbody formArrayName="items">
          <tr *ngFor="let item of items.controls; let i = index" [formGroupName]="i">
            <td class="">
              <app-dropdown-search-lazy fieldClass="w-full fuse-mat-dense" formControlName="productId" [items]="products | async"
                [config]="productConfig" (selected)="onProductSelected($event, item)" [showClearButton]="false"></app-dropdown-search-lazy>
            </td>
            <td class="">
              <mat-form-field class="w-full fuse-mat-dense">
                <input matInput type="number" formControlName="quantity">
              </mat-form-field>
            </td>
            <td class="">
              <mat-form-field class="w-full fuse-mat-dense">
                <input matInput type="number" formControlName="unitCost">
              </mat-form-field>
            </td>
            <td class="">
              <mat-form-field class="w-full fuse-mat-dense">
                <input matInput type="number" formControlName="totalAmount">
              </mat-form-field>
            </td>
            <td class="">
              <mat-form-field class="w-full fuse-mat-dense">
                <input matInput formControlName="notes">
              </mat-form-field>
            </td>
            <td class="">
              <button class="-mt-10" mat-icon-button [color]="'warn'" (click)="removeRow(i)">
                <mat-icon svgIcon="heroicons_outline:trash"></mat-icon>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </form>
  </div>
</div>