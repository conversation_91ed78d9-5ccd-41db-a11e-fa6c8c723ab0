import { Routes } from '@angular/router';
import { ReceiptComponent } from './receipt.component';
import { ReceiptCreateComponent } from './receipt-create/receipt-create.component';
import { ReceiptPageComponent } from './receipt-page/receipt-page.component';
import { inject } from '@angular/core';
import { ReceiptService } from './receipt.service';

export default [
    {
        path: '',
        component: ReceiptComponent,
        resolve: {
            products: () => inject(ReceiptService).getProducts(),
            vendors: () => inject(ReceiptService).getVendors(),
        },
        children: [
            {
                path: '',
                component: ReceiptPageComponent,
            },
            {
                path: 'create',
                component: ReceiptCreateComponent,
            },
            {
                path: ':id',
                component: ReceiptCreateComponent,
            },
        ]
    },
] as Routes;
