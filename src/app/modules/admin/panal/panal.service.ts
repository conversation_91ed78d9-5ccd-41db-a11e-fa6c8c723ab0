import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { removeEmpty } from 'app/modules/common/helper';
import { environment } from 'environments/environment';
import { toUpper } from 'lodash';
import { BehaviorSubject, map, tap } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PanalService {

  private _categories: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _units: BehaviorSubject<any[] | null> = new BehaviorSubject(null);

  get categories$() {
    return this._categories.asObservable();
  }

  get units$() {
    return this._units.asObservable();
  }

  constructor(private http: HttpClient) { }

  datatable(dataTablesParameters: any) {
    const { columns, order, search, start, length, filter } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;

    return this.http.get(environment.apiUrl + '/api/panel/datatables', {
      params: {
        page: page,
        limit: length,
        sortBy: sortBy,
        search: search.value,
        ...removeEmpty(filter)
      }
    }).pipe(
      map((resp: any) => {
        resp.data.forEach((e: any, i: number) => e.no = start + i + 1);
        return resp;
      })
    );
  }

  getBranchs() {
    return this.http.get(environment.apiUrl + '/api/branch')
  }


  getProducts() {
    return this.http.get(environment.apiUrl + '/api/product').pipe(
      tap((resp: any) => {
        this._categories.next(resp);
      }),
    )
  }

  getCategories() {
    return this.http.get(environment.apiUrl + '/api/category').pipe(
      tap((resp: any) => {
        this._categories.next(resp);
      }),
    )
  }
  getUnit() {
    return this.http.get(environment.apiUrl + '/api/unit').pipe(
      tap((resp: any) => {
        this._units.next(resp);
      }),
    )
  }

  getById(id: any) {
    return this.http.get(environment.apiUrl + '/api/panel/' + id).pipe(
      tap((resp: any) => {
        this._units.next(resp);
      }),
    )
  }

  delete(id: number) {
    return this.http.delete(environment.apiUrl + '/api/panel/' + id)
  }

  post(data: { name: string, id: number }) {
    return this.http.post(environment.apiUrl + '/api/panel/', data)
  }

  create(data: any) {
    return this.http.post('/api/panel', data)
  }
  update(id: any, data: any) {
    return this.http.put('/api/panel/' + id, data)
  }
  getPro(id: number) {
    return this.http.get(environment.apiUrl + '/api/panel/' + id)
  }

  // getmanagepanal(data:any) {
  //   const datas ={
  //     id: data
  //   }
  //   return this.http.post(environment.apiUrl + '/api/panel/layout',data)
  // }
  getmanagepanel(panelIds: number[]) { // Corrected function parameter to 'panelIds'
    const payload = {
      panelIds: panelIds
    };

    return this.http.post(environment.apiUrl + '/api/panel/layout', payload, {
      headers: new HttpHeaders({
        'Content-Type': 'application/json' // Changed 'Content-Type' to 'application/json'
      })
    });
  }

  getpanal(id: any) {
    return this.http.get(environment.apiUrl + '/api/panel', { params: { branchId: id } })
  }

  getdevice(branchId: any) {
    return this.http.get(environment.apiUrl + '/api/device', { params: { branchId: branchId } })
  }



}
