import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, Inject, OnInit, ViewContainerRef } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogActions,
  MatDialogClose,
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';
import { MAT_FORM_FIELD_DEFAULT_OPTIONS, MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatToolbarModule } from '@angular/material/toolbar';
import { FilePickerModule } from 'ngx-awesome-uploader';
import { NgxMaskDirective } from 'ngx-mask';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { PanalService } from '../panal.service';
import { DndModule } from 'ngx-drag-drop';
import { ColorPickerService, Cmyk } from 'ngx-color-picker';
import { ColorPickerModule } from 'ngx-color-picker';
import { CdkDragDrop, CdkDropList, CdkDrag, moveItemInArray } from '@angular/cdk/drag-drop';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-product-compose',
  standalone: true,
  templateUrl: './panal-compose.component.html',
  styleUrls: ['./panal-compose.component.scss'],
  imports: [CommonModule, MatIconModule, MatFormFieldModule, MatInputModule,
    FormsModule, MatToolbarModule, MatButtonModule, MatDialogTitle, MatDialogContent, MatDialogActions,
    MatDialogClose, MatSelectModule, FilePickerModule, NgxMaskDirective, ReactiveFormsModule, NgxMatSelectSearchModule, DndModule
    , ColorPickerModule, CdkDropList, CdkDrag, MatSlideToggleModule
  ],
  providers: [
    ColorPickerService,
    { provide: MAT_FORM_FIELD_DEFAULT_OPTIONS, useValue: { appearance: 'outline' } }
  ]
})
export class PanalComposeComponent implements OnInit {
  form: FormGroup;
  protected product: any[] = [];
  protected branchs: any[] = [];
  productFilter: any[] = [];
  public color: string = '127bdc';
  public presetValues: string[] = [];
  public selectedColor: string = 'color1';
  public toggle: boolean = false;
  devices: any
  constructor(
    private dialogRef: MatDialogRef<PanalComposeComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialog: MatDialog,
    private fb: FormBuilder,
    public PanalService: PanalService,
    public vcRef: ViewContainerRef,
    private cpService: ColorPickerService,
    private toastr: ToastrService,
  ) {
    const branchId = 0
    this.PanalService.getdevice(branchId).subscribe({
      next:(resp: any) => {
        console.log('devices: ',resp);
        
        this.devices = resp
      },error(err) {
        this.toastr.error('err: ',err)
          //console.log('err: ',err);
      },
    })
  }

  ngOnInit(): void {
    if (this.data.type === 'EDIT') {
      let panelProduct: any[] = [];
      for (let p of this.data.value.panelProducts) {
        let jsObject = {
          name: p.product.name,
          id: p.product.id,
          txtColor: p.txtColor,
          bgColor: p.bgColor,
          code: p.product.code
        };
        panelProduct.push(jsObject);
      }
      
      this.form = this.fb.group({
        //device: [this?.data?.value?.device?.name],
        name: [this?.data?.value?.name],
        branchId: [this?.data?.value?.branch?.id],
        productIds: [''],
        panelProducts: this.fb.array(panelProduct.map(product => this.createProductGroup(product))),
        productFilterCtrl: [''],
        active: [this?.data?.value?.active]
      });
    // console.log(this.form.value.panelProducts)
    } else {
      this.form = this.fb.group({
        //device: ['', Validators.required],
        name: ['', Validators.required],
        branchId: ['', Validators.required],
        productIds: [''],
        panelProducts: this.fb.array([]),
        productFilterCtrl: [''],
        active: true
      });
    }

    this.PanalService.getProducts().subscribe({
      next: (resp) => {
        const products = resp.map(e => ({ ...e, itemName: `(${e.code}) ${e.name}` }))
        this.product = products;
        this.productFilter = products;
      },error: () => {
        this.toastr.error('เกิดข้อผิดพลาด')
      }
    });

    this.PanalService.getBranchs().subscribe({
      next: (resp: any) => {
        this.branchs = resp;
      },
      error: (err) => {
        alert(JSON.stringify(err));
      }
    });

    this.form.get('productFilterCtrl').valueChanges.subscribe(
      (search) => {
        this.productFilter = this.product.filter(p => p.itemName.includes(search));
      }
    );

    this.form.get('productIds').valueChanges.subscribe(
      (values) => {
        // console.log(values);
      }
    );
  }

  get panelProducts(): FormArray {
    return this.form.get('panelProducts') as FormArray;
  }

  createProductGroup(product?: any): FormGroup {
    console.log('createProductGroup: ',product);
    
    return this.fb.group({
      productId: [product?.id || '', Validators.required],
      txtColor: [product?.txtColor || ''],
      bgColor: [product?.bgColor || ''],
      productName: [product?.name || ''],
      code: [product?.code || '']
    });
  }

  AddProduct(item?: any) {
    this.panelProducts.push(this.createProductGroup(item));
    // console.log(this.panelProducts.value);
  }

  RemoveProduct(index: number) {
    this.panelProducts.removeAt(index);
  }

  clickAddProduct() {
    const productControl = this.form.get('productIds');
    if (productControl && productControl.value) {
      const foundProduct = this.product.find(p => p.id === productControl.value);
      if (foundProduct) {
        this.AddProduct(foundProduct);
      }
      productControl.reset();
    }
  }

  Submit() {
    if (this.form.invalid) {
      return;
    }

    if (this.data.type === 'EDIT') {
      this.PanalService.update(this.form.value, this.data.value.id).subscribe({
        next: (resp: any) => {
          this.dialogRef.close();
        },error: () => {
          this.toastr.error('เกิดข้อผิดพลาด')
        }
      });
    } else {
      this.PanalService.post(this.form.value).subscribe({
        next: (resp: any) => {
          this.dialogRef.close();
        },error: () => {
          this.toastr.error('เกิดข้อผิดพลาด')
        }
      });
    }
  }

  onClose() {
    this.dialogRef.close();
  }

  drop(event: CdkDragDrop<string[]>) {
    const panelProductsArray = this.panelProducts.controls;
    moveItemInArray(panelProductsArray, event.previousIndex, event.currentIndex);
    this.panelProducts.setValue(panelProductsArray.map(control => control.value));
  }
}
