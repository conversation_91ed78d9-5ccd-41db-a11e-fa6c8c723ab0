<div class="flex flex-col ">
    <mat-toolbar class="flex justify-between">
        <span class="example-spacer"></span>

        <div>
            <span class="font-bold text-[20px]" *ngIf="this.data.type === 'EDIT'">แก้ไขหน้าจอ</span>
            <span class="font-bold text-[20px]" *ngIf="this.data.type === 'NEW'">เพิ่มหน้าจอ</span>
        </div>

        <button mat-icon-button mat-dialog-close class="example-icon " aria-label="Example icon-button with menu icon">
            <mat-icon svgIcon="heroicons_outline:x-mark"></mat-icon>
        </button>
    </mat-toolbar>
    <form [formGroup]="form">
        <div class="flex flex-col justify-between">
            <div class="row-auto">
                <div class="flex row-auto p-8 pb-4 mt-8 overflow-hidden gap-4 h-fit bg-card rounded-2xl">
                    <div class="w-1/3">
                        <mat-form-field class="flex-auto w-full">
                            <mat-label>ชื่อหน้าจอ</mat-label>
                            <input matInput [placeholder]="'กรอก'" [formControlName]="'name'">
                            <mat-error *ngIf="form.get('name').hasError('required')">กรุณากรอกชื่อหน้าจอ</mat-error>
                        </mat-form-field>

                        <mat-form-field class="flex-auto w-full">
                            <mat-label>สาขา</mat-label>
                            <mat-select [formControlName]="'branchId'">
                                <mat-option *ngFor="let branch of branchs"
                                    [value]="branch.id">{{branch.name}}</mat-option>
                            </mat-select>
                            <mat-error *ngIf="form.get('branchId').hasError('required')">กรุณาเลือกสาขา</mat-error>
                        </mat-form-field>
                        <!--<mat-form-field class="flex-auto w-full">
                            <mat-label>อุปกรณ์</mat-label>
                            <mat-select [formControlName]="'device'">
                                <mat-option *ngFor="let device of devices"
                                    [value]="device.name">{{device.name}}</mat-option>
                            </mat-select>
                            <mat-error *ngIf="form.get('device').hasError('required')">กรุณาเลือกอุปกรณ์</mat-error>
                        </mat-form-field>-->
                        <div class="w-full mb-4">
                            <mat-slide-toggle [formControlName]="'active'" [color]="'primary'">แสดง</mat-slide-toggle>
                        </div>
                    </div>
                    <div class="w-full">
                        <div class="flex items-center flex-auto gap-1">
                            <mat-form-field class="w-full">
                                <mat-label>สินค้า</mat-label>
                                <mat-select [formControlName]="'productIds'">
                                    <mat-option>
                                        <ngx-mat-select-search [placeholderLabel]="'ค้นหาสินค้า'"
                                            [formControlName]="'productFilterCtrl'"
                                            [noEntriesFoundLabel]="'ไม่พบข้อมูล'"></ngx-mat-select-search>
                                    </mat-option>
                                    <mat-option *ngFor="let product of productFilter"
                                        [value]="product.id">{{product.itemName}}</mat-option>
                                </mat-select>
                            </mat-form-field>

                            <button mat-icon-button [color]="'primary'" class="mb-5" (click)="clickAddProduct()"
                                [disabled]="!form.get('productIds').value">
                                <mat-icon class="icon-size-10" [svgIcon]="'heroicons_solid:plus-circle'"></mat-icon>
                            </button>
                        </div>
                        <div
                            class="flex flex-col overflow-y-auto border-2 border-solid border-neutral-90 bg-gray-50 w-full rounded-md h-auto p-5">
                            <div class="inline-grid grid-cols-5 gap-3">
                                <div class="col-span-2">
                                    <a>ชื่อสินค้า</a>
                                </div>
                                <div class="text-center">
                                    <a>สีตัวอักษร</a>
                                </div>
                                <div class="text-center">
                                    <a>สีพื้นหลัง</a>
                                </div>
                                <div class="text-center">
                                    <a>ลบ</a>
                                </div>
                            </div>
                            <div cdkDropList (cdkDropListDropped)="drop($event)">
                                <div *ngFor="let product of panelProducts.controls; let index = index" cdkDrag
                                    class="flex flex-col m-0 w-full justify-between mb-4">
                                    <div class="inline-grid grid-cols-5 gap-3 items-center">
                                        <div class="col-span-2">
                                            ({{ product.value.code}})
                                            {{ product.value.productName }}
                                        </div>
                                        <div class="flex justify-center">
                                            <input class="w-10 border-2 border-dotted border-sky-500"
                                                [(colorPicker)]="product.value.txtColor" [cpOutputFormat]="'hex'"
                                                [style.background]="product.value.txtColor" [cpOKButton]="true"
                                                [cpOKButtonText]="'Select'" [cpCancelButton]="true"
                                                [cpSaveClickOutside]="false" [cpDisableInput]="false"
                                                [cpAlphaChannel]="'disabled'" [cpPresetColors]="presetValues"
                                                [cpAddColorButton]="true" />
                                        </div>
                                        <div class="flex justify-center">
                                            <input class="w-10 border-2 border-dotted border-sky-500"
                                                [(colorPicker)]="product.value.bgColor" [cpOutputFormat]="'hex'"
                                                [style.background]="product.value.bgColor" [cpOKButton]="true"
                                                [cpOKButtonText]="'Select'" [cpCancelButton]="true"
                                                [cpSaveClickOutside]="false" [cpDisableInput]="false"
                                                [cpAlphaChannel]="'disabled'" [cpPresetColors]="presetValues"
                                                [cpAddColorButton]="true" />
                                        </div>
                                        <div class="flex items-center justify-center">
                                            <button mat-icon-button [color]="'primary'" class=""
                                                (click)="RemoveProduct(index)">
                                                <mat-icon [svgIcon]="'heroicons_outline:trash'"></mat-icon>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bottom-0 flex justify-end w-full px-8 py-5 mt-8 bg-white border-t dark:bg-gray-700">
                <div mat-dialog-actions class="flex justify-end">
                    <button type="submit" class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
                        บันทึก
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>
