import { Subject, Subscription } from 'rxjs';
import { Component, OnInit, OnChanges, Inject, ViewChild } from '@angular/core';
import { CommonModule, CurrencyPipe, DecimalPipe } from '@angular/common';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import {
    MatDialog,
    MatDialogTitle,
    MatDialogContent,
    MatDialogActions,
    MatDialogClose,
    MatDialogRef,
    MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { FormBuilder, FormControl, FormGroup, FormsModule, Validators } from '@angular/forms';
import { MatToolbarModule } from '@angular/material/toolbar';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
// import { CreditService } from '../credit.service';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { ToastrService } from 'ngx-toastr';
import { MatRadioModule } from '@angular/material/radio';
import { createFileFromBlob } from 'app/modules/shared/helper';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { ProductService } from '../../product/product.service';
import { StatusBadgeComponent } from 'app/modules/shared/status-badge/status-badge.component';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
    selector: 'app-user-form',
    standalone: true,
    templateUrl: './dialog.component.html',
    styleUrl: './dialog.component.scss',
    providers: [
        CurrencyPipe,
        DecimalPipe
    ],
    imports: [CommonModule, DataTablesModule, MatIconModule, MatFormFieldModule, MatInputModule,
        FormsModule, MatToolbarModule,
        MatButtonModule,
        MatDialogTitle,
        MatDialogContent,
        MatDialogActions,
        MatDialogClose,
        MatSelectModule,
        ReactiveFormsModule,
        MatInputModule,
        MatFormFieldModule,
        MatRadioModule,
        StatusBadgeComponent,
        MatTooltipModule
    ]
})
export class DialogSelectProductComponent implements OnInit {

    form: FormGroup;
    stores: any[] = [];
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    addForm: FormGroup;
    category: any[] = []
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    catagories: any[] = [];
    @ViewChild('activeStatus') activeStatus: any;
    @ViewChild('btNg') btNg: any;
    @ViewChild('btPicture') btPicture: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;


    constructor(
        private dialogRef: MatDialogRef<DialogSelectProductComponent>,
        @Inject(MAT_DIALOG_DATA) public data: any,
        public dialog: MatDialog,
        private FormBuilder: FormBuilder,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        private _service: ProductService,
        private currencyPipe: CurrencyPipe,
        private decimalPipe: DecimalPipe,

    ) {
        this._service.getCategories().subscribe((resp: any) => {
            this.category = resp
        })
        this.form = this.FormBuilder.group({
            category_id: ''
        })



        // console.log('1111',this.data?.type);

    }

    ngOnInit(): void {
        setTimeout(() =>
            this.loadTable());
    }

    onChangeType() {
        this.rerender()
    }


    loadTable(): void {
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,     // Set the flag
            ajax: (dataTablesParameters: any, callback) => {

                dataTablesParameters.filter = {
                    'filter.category.id': this.form.value.category_id ?? ''
                }

                this._service.datatable(dataTablesParameters).subscribe({
                    next: (resp: any) => {
                        callback({
                            recordsTotal: resp.meta.totalItems,
                            recordsFiltered: resp.meta.totalItems,
                            data: resp.data
                        });
                    }, error: () => {
                        this.toastr.error('เกิดข้อผิดพลาด')
                    }
                })
            },
            columns: [

                {
                    title: 'ลำดับ',
                    data: 'no',
                    className: 'w-15 text-center'
                },
                {
                    title: 'รหัสสินค้า',
                    data: 'code',
                    defaultContent: '-',
                    className: 'w-30 text-center'
                },
                {
                    title: 'Barcode',
                    data: 'barcode',
                    defaultContent: '-',
                    className: 'w-30 text-center'
                },
                {
                    title: 'ชื่อสินค้า',
                    data: 'name',
                    className: 'text-start'
                },
                {
                    title: 'ราคา',
                    data: 'price',
                    ngPipeInstance: this.currencyPipe,
                    ngPipeArgs: ['THB', 'symbol', '1.2-2'],
                    className: 'text-center'
                },
                {
                    title: 'หน่วยนับ',
                    data: 'unit.name',
                    defaultContent: '-',
                    className: 'text-center'
                },

                {
                    title: 'แสดง',
                    data: null,
                    defaultContent: '',
                    ngTemplateRef: {
                        ref: this.activeStatus,
                    },
                    className: 'w-30 text-center'
                },
                {
                    title: 'จัดการ',
                    data: null,
                    defaultContent: '',
                    ngTemplateRef: {
                        ref: this.btNg,
                    },
                    className: 'w-15 text-center'
                },
            ]
        }
    }

    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(this.dtOptions);
        });
    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }


    openDialogEdit(data: any) {
        this.dialogRef.close(data)
    }

    onClose() {
        this.dialogRef.close()
    }

}
