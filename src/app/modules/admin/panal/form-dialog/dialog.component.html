<div class="flex flex-col flex-auto min-w-0">
    <div class="flex-auto p-0 m-0 rounded-md sm:p-10 bg-card">
        <div class="flex flex-row justify-between pb-2 my-5">
            <div>
                <h2 class="text-2xl font-extrabold leading-7 tracking-tight truncate md:text-xl sm:leading-10">
                    รายการสินค้า
                </h2>
            </div>
            <form [formGroup]="form">
                <div class="md:w-full w-full min-w-[350px]">
                    <mat-form-field class="w-full">
                        <mat-label>เลือกประเภทสินค้า</mat-label>
                        <mat-select [formControlName]="'category_id'" (selectionChange)="onChangeType()">
                            <mat-option [value]="">
                                ทั้งหมด
                            </mat-option>
                            <mat-option *ngFor="let item of category;" [value]="item.id">
                                {{item.name}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
            </form>
        </div>

        <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="row-border hover w-full">
        </table>
    </div>
</div>

<ng-template #btNg let-data="adtData">
    <mat-icon [svgIcon]="'heroicons_solid:check'" matTooltip="เลือกสินค้า" matTooltipPosition="above"
        class="cursor-pointer text-primary" (click)="openDialogEdit(data)">
    </mat-icon>
</ng-template>


<ng-template #btPicture let-data="adtData">
    <button *ngIf="data.imageUrl" mat-icon-button [color]="'primary'" (click)="showPicture(data.imageUrl)">
        <mat-icon class="icon-size-5" [svgIcon]="'mat_solid:image'"></mat-icon>
    </button>
</ng-template>

<ng-template #activeStatus let-data="adtData">
    <app-status-badge [active]="data.active"></app-status-badge>
    <ng-template #inactiveTemplate>
        <div>
            <span class="bg-red-300 rounded-xl p-2 font-semibold">ปิดใช้งาน</span>
        </div>
    </ng-template>
</ng-template>

<ng-template #vatStatus let-data="adtData">
    <div *ngIf="data.vatStatus; else inactiveTemplate">
        <span class="bg-green-300 rounded-xl py-2 px-6 font-semibold">true</span>
    </div>
    <ng-template #inactiveTemplate>
        <div>
            <span class="bg-red-300 rounded-xl py-2 px-6 font-semibold">false</span>
        </div>
    </ng-template>
</ng-template>
<ng-template #remarkStatus let-data="adtData">
    <div *ngIf="data.remarkStatus; else inactiveTemplate">
        <span class="bg-green-300 rounded-xl py-2 px-6 font-semibold">true</span>
    </div>
    <ng-template #inactiveTemplate>
        <div>
            <span class="bg-red-300 rounded-xl py-2 px-6 font-semibold">false</span>
        </div>
    </ng-template>
</ng-template>