<div class="w-full p-5 bg-gray-50">
    <!-- Branch Selector -->
    <div class="w-full mb-4">
        <div class="md:w-1/3 mx-auto p-5 w-full">
            <mat-form-field class="w-full">
                <mat-label>สาขา</mat-label>
                <mat-select formControlName="branchId" (selectionChange)="onbranchsSelect($event)">
                    <mat-option *ngFor="let branch of branchs" [value]="branch.id">{{ branch.name }}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
    </div>

    <!-- Drag and Drop Panel -->
    <div class="w-full mb-4">
        <div class="text-center">
            <div cdkDropListGroup>
                <div class="flex flex-wrap justify-center mt-4">
                    <div cdkDropList class="example-list" (cdkDropListDropped)="drop($event)">
                        <ng-container *ngFor="let panal of panal; track: track Panal">
                            <div class="example-box bg-white p-3 m-2 rounded shadow-md" cdkDrag>
                                <div class="example-custom-placeholder" *cdkDragPlaceholder></div>
                                {{ panal.name }}
                            </div>
                        </ng-container>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Save Button -->
    <div class="w-full flex justify-center">
        <button mat-flat-button color="primary" class="flex items-center px-4 py-2" (click)="save()">
            <mat-icon [svgIcon]="'feather:save'"></mat-icon>
            <span class="ml-2">บันทึก</span>
        </button>
    </div>
</div>
