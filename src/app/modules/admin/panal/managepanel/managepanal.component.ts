import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewContainerRef } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MatDialog,
  MatDialogActions,
  MatDialogClose,
  MatDialogContent,
  MatDialogTitle,
} from '@angular/material/dialog';
import { MAT_FORM_FIELD_DEFAULT_OPTIONS, MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatToolbarModule } from '@angular/material/toolbar';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { PanalService } from '../panal.service';
import { DndModule } from 'ngx-drag-drop';
import { MatSelectChange } from '@angular/material/select';
import {
  CdkDrag,
  CdkDragDrop,
  CdkDropList,
  moveItemInArray,
} from '@angular/cdk/drag-drop';
import { ToastrService } from 'ngx-toastr';
@Component({
  selector: 'app-product-compose',
  standalone: true,
  templateUrl: './managepanal.component.html',
  styleUrls: ['./managepanal.component.scss'],
  imports: [CommonModule, MatIconModule, MatFormFieldModule, MatInputModule,
    FormsModule, MatToolbarModule, MatButtonModule, MatDialogTitle, MatDialogContent, MatDialogActions,
    MatDialogClose, MatSelectModule, ReactiveFormsModule, NgxMatSelectSearchModule, DndModule,
    CdkDropList, CdkDrag,
  ],
  providers: [
    { provide: MAT_FORM_FIELD_DEFAULT_OPTIONS, useValue: { appearance: 'outline' } }
  ]
})
export class ManageanalComponent implements OnInit {
  protected branchs: any[] = [];
  panal: any;
  branchsid: any;

  constructor(
    public dialog: MatDialog,
    public PanalService: PanalService,
    public vcRef: ViewContainerRef,
    private toastr: ToastrService,
  ) { }

  ngOnInit(): void {
    this.PanalService.getBranchs().subscribe({
      next: (resp: any) => {
        this.branchs = resp;
      },
      error: (err) => {
        alert(JSON.stringify(err));
        this.toastr.error('เกิดข้อผิดพลาด')
      }
    });

  }

  onbranchsSelect(event: MatSelectChange) {
    this.branchsid = event.value

    this.PanalService.getpanal(this.branchsid).subscribe({
      next: (resp: any) => {
        this.panal = resp;
      },
      error: (err) => {
        alert(JSON.stringify(err));
        this.toastr.error('เกิดข้อผิดพลาด')
      }
    });
  }

  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.panal, event.previousIndex, event.currentIndex);
  }

  save() {
    const panalIds = this.panal.map(p => p.id); // Extract IDs from the panal array

    this.PanalService.getmanagepanel(panalIds).subscribe({
      next: (resp: any) => {
        this.toastr.success('ดำเนินการสำเร็จ')
      },error: () => {
        this.toastr.error('เกิดข้อผิดพลาด')
      }
    });
  }
}
