<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto p-4 m-4 rounded-md sm:p-10 bg-card">

    <div class="flex flex-row justify-between pb-2 my-5 ">
      <div>
        <h2 class="text-2xl font-extrabold leading-7 tracking-tight truncate md:text-xl sm:leading-10">
          รายการ Panel
        </h2>
      </div>
      <div class="flex gap-2">
        <!-- <button mat-flat-button [color]="'primary'" (click)="openpanel()">
          <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:plus-circle'"></mat-icon>
          <span class="ml-2"> จัดการ Panel</span>
        </button> -->
        <button mat-flat-button [color]="'primary'" (click)="opendialogapro()">
          <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:plus-circle'"></mat-icon>
          <span class="ml-2"> เพิ่ม Panel</span>
        </button>
      </div>
    </div>
    <!-- <div class="md:w-1/4 w-full">
      <mat-form-field class="w-full">
        <mat-label>เลือกสาขา</mat-label>
        <mat-select [(ngModel)]="filterBranch" (selectionChange)="onChangeBranch()">
          <mat-option [value]=""> ทั้งหมด </mat-option>
          <mat-option *ngFor="let item of branches" [value]="item.id"> {{ item.name }} </mat-option>
        </mat-select>
      </mat-form-field>
    </div> -->
    <div class="overflow-auto">
      <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="row-border hover w-full">
      </table>
    </div>

  </div>
</div>
<ng-template #activeStatus let-data="adtData">
  <app-status-badge [active]="data.active"></app-status-badge>
</ng-template>
<ng-template #btNg let-data="adtData">
  <button mat-icon-button [color]="'primary'" [matMenuTriggerFor]="menu">
    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cog-8-tooth'"></mat-icon>
  </button>
  <mat-menu #menu="matMenu">
    <button mat-menu-item (click)="openDialogEdit(data)">
      <mat-icon [svgIcon]="'heroicons_solid:pencil-square'"></mat-icon>
      <span>แก้ไข</span>
    </button>
    <mat-divider></mat-divider>
    <button mat-menu-item (click)="clickDelete(data.id)">
      <mat-icon [svgIcon]="'heroicons_solid:trash'"></mat-icon>
      <span>ลบ</span>
    </button>
  </mat-menu>
</ng-template>