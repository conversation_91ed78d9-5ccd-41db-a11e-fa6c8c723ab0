import { Routes } from '@angular/router';
import { PanalComponent } from './panal.component';
import { inject } from '@angular/core';
import { PanalService } from './panal.service';
import { ManageanalComponent } from './managepanel/managepanal.component';
import { FormPanalComponent } from './form/form.component';

export default [
    {
        path: '',
        component: PanalComponent,
        resolve  : {
            // categories: () => inject(PanalService).getCategories(),
            // products  : () => inject(InventoryService).getProducts(),
        },
    },
    {
        path    : 'managepanel',
        component: ManageanalComponent,
    },
    {
        path    : 'form',
        component: FormPanalComponent,
    },
    {
        path    : 'edit/:id',
        component: FormPanalComponent,
    }
] as Routes;
