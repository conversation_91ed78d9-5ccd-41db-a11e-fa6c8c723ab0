<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto p-6 m-4 rounded-lg shadow-sm bg-white">
    <div class="flex flex-row justify-between pb-4 mb-6 border-b border-gray-100">
      <div>
        <h2 class="text-xl font-bold mb-4">ตั้งค่าหน้าจอ POS</h2>
        <p class="text-sm text-gray-500">จัดการหน้าจอสินค้า</p>
      </div>
    </div>

    <form [formGroup]="form" class="p-4">
      <mat-form-field class="w-full mb-4" appearance="fill">
        <mat-label>ชื่อหน้าจอ</mat-label>
        <input matInput type="text" formControlName="name" placeholder="ระบุชื่อหน้าจอ">
      </mat-form-field>

      <mat-checkbox formControlName="active" class="mb-4">เปิดใช้งาน</mat-checkbox>
      <div formArrayName="panelProducts" class="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-5 gap-4">
        <mat-card *ngFor="let group of form?.get('panelProducts')?.['controls']; let i = index" [formGroupName]="i"
          class="p-4 shadow-md border border-gray-200 rounded-xl flex flex-col gap-3">

          <!-- รูปภาพสินค้า -->
          <div class="w-full h-32 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden">
            <img *ngIf="group.get('image')?.value; else noImage" [src]="group.get('image').value" alt="รูปสินค้า"
              class="h-full w-full object-cover">
            <ng-template #noImage>
              <span class="text-sm text-gray-500">ไม่มีรูปภาพ</span>
            </ng-template>
          </div>

          <!-- ชื่อสินค้า -->
          <div class="text-base font-semibold text-gray-800 flex items-center gap-2 cursor-pointer"
            (click)="onSelectProduct(group, i)">
            <div class="w-4 h-4 rounded-full border border-gray-300"
              [ngStyle]="{'background-color': group.get('color').value}">
            </div>
            {{ i + 1 }}.{{ group.get('productName').value || 'คลิกเพื่อเลือกสินค้า' }}
          </div>

          <!-- เลือกสี -->
          <div class="flex items-center gap-2">
            <label class="text-sm font-medium text-gray-600">สี:</label>
            <input type="color" formControlName="color" class="h-8 w-8 border rounded cursor-pointer" />
          </div>
        </mat-card>
      </div>
    </form>
    <!-- Submit -->
    <div class="md:col-span-2 flex justify-end gap-3 pt-4 border-t border-gray-100">
      <button mat-stroked-button color="basic" type="button">
        ยกเลิก
      </button>
      <button mat-flat-button color="primary" class="px-6" (click)="onSubmit()">
        <mat-icon>save</mat-icon>
        บันทึกข้อมูล
      </button>
    </div>

  </div>
</div>