import { CommonModule, Cur<PERSON>cyPipe, DecimalPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, OnInit, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';

import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { FilePickerModule } from 'ngx-awesome-uploader';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';

import { FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { StatusBadgeComponent } from 'app/modules/shared/status-badge/status-badge.component';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { ImageUploadService } from 'app/modules/common/image-upload/image-upload.service';
import { ImageUploadComponent } from 'app/modules/common/image-upload/image-upload.component';
import { PanalService } from '../panal.service';
import { CdkDragDrop, DragDropModule } from '@angular/cdk/drag-drop';
import { DialogSelectProductComponent } from '../form-dialog/dialog.component';
@Component({
    selector: 'app-form-product',
    standalone: true,
    providers: [
        CurrencyPipe,
        DecimalPipe
    ],
    imports: [
        CommonModule,
        MatButtonModule,
        MatIconModule,
        FilePickerModule,
        MatMenuModule,
        MatDividerModule,
        ReactiveFormsModule,
        FormsModule,
        MatSelectModule,
        MatInputModule,
        MatFormFieldModule,
        MatCheckboxModule,
        ImageUploadComponent,
        DragDropModule

    ],
    templateUrl: './form.component.html',
    styleUrl: './form.component.scss',
    changeDetection: ChangeDetectionStrategy.Default,
})
export class FormPanalComponent implements OnInit, AfterViewInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    catagories: any[] = [];
    categories: any[] = [];
    units: any[] = [];
    vendors: any[] = [];
    branches: any[] = [];
    item: any
    form: FormGroup;
    productForm: FormGroup
    Id: any
    constructor(
        private _service: PanalService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        public dialog: MatDialog,
        private _router: Router,
        private fb: FormBuilder,
        private currencyPipe: CurrencyPipe,
        private decimalPipe: DecimalPipe,
        private imageUploadService: ImageUploadService,
        private _activatedRoute: ActivatedRoute
    ) {
        this.Id = this._activatedRoute.snapshot.params.id
        this._service.categories$.subscribe(resp => this.catagories = resp);
        this.form = this.fb.group({
            name: [null, Validators.required],
            active: [true],
            panelProducts: this.fb.array([])
        });
    }
    ngOnInit(): void {
        if (this.Id) {
            this._service.getById(this.Id).subscribe((resp) => {
                const data = resp;
                this.item = resp;
                this.form = this.fb.group({
                    name: [data.name, Validators.required],
                    active: [data.active],
                    panelProducts: this.fb.array([]) // กรณีดึงข้อมูลมาใส่เองภายหลัง
                });
                // สมมุติว่ามีข้อมูลสินค้าใน data.panelProducts
                const panelArray = this.form.get('panelProducts') as FormArray;
                data.panelProducts.forEach(p => {
                    const formValue = this.fb.group({
                        id: p.id,
                        productName: p.product?.name,
                        image: p.product?.imageUrl ?? null,
                        productId: p.product?.id,
                        color: p.color ?? '#dedede'
                    })
                    panelArray.push(formValue);
                });
            });
        } else {
            this.form = this.fb.group({
                name: [null, Validators.required],
                active: [true],
                panelProducts: this.fb.array(this.createInitialProducts())
            });
        }
    }


    createInitialProducts(): FormGroup[] {
        const products: FormGroup[] = [];

        for (let i = 1; i <= 20; i++) {
            products.push(this.fb.group({
                productName: [null],
                image: [null],
                productId: [null, Validators.required],
                color: ['#dedede', Validators.required]
            }));
        }

        return products;
    }

    drop(event: CdkDragDrop<string[]>) {
        const formArray = this.form.get('panelProducts') as FormArray;
        const prev = formArray.at(event.previousIndex);
        const curr = formArray.at(event.currentIndex);

        formArray.removeAt(event.previousIndex);
        formArray.insert(event.currentIndex, prev);
    }

    ngAfterViewInit() {


    }

    onSelectProduct(group: FormGroup, index) {
        const dialogRef = this.dialog.open(DialogSelectProductComponent, {
            width: 'calc(100% - 30px)',
            height: 'calc(100% - 30px)',
            enterAnimationDuration: 300,
            exitAnimationDuration: 300,
            data: {
                value: ''
            },
            maxWidth: "100%",
            maxHeight: 'calc(100% - 30px)'
        });

        dialogRef.afterClosed().subscribe((result) => {
            if (result) {
                group.patchValue({
                    productId: result.id,
                    productName: result.name,
                    image: result.imageUrl,
                    color: result.color || group.get('color')?.value // ถ้าไม่เลือกสีใหม่ให้ค่าสีเดิม
                });
            }
        });
    }



    onSubmit() {
        const confirmation = this.fuseConfirmationService.open({
            title: "ยืนยันการบันทึกข้อมูล",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "primary"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "ยืนยัน",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "ยกเลิก"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    if (this.Id) {
                        this._service.update(this.Id, this.form.value).subscribe({
                            error: (err) => {
                                this.toastr.error(err.error.message)
                            },
                            complete: () => {
                                this.toastr.success('ดำเนินการแก้ไขข้อมูลสำเร็จ')
                                this._router.navigate(['panel'])
                            },
                        });
                    } else {
                        this._service.create(this.form.value).subscribe({
                            error: (err) => {
                                this.toastr.error(err.error.message)
                            },
                            complete: () => {
                                this.toastr.success('ดำเนินการเพิ่มข้อมูลสำเร็จ')
                                this._router.navigate(['panel'])
                            },
                        });
                    }
                }
            }
        )
    }
}

