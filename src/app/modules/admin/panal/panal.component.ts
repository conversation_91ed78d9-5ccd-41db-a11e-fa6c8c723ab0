
import { CommonModule, DatePipe } from '@angular/common';
import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { FilePickerModule } from 'ngx-awesome-uploader';
import { PanalComposeComponent } from './product-compose/panal-compose.component';
import { PanalService } from './panal.service';
import { Subject } from 'rxjs';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInput } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { StatusBadgeComponent } from 'app/modules/shared/status-badge/status-badge.component';

@Component({
  selector: 'app-products',
  standalone: true,
  imports: [
    CommonModule,
    DataTablesModule,
    MatButtonModule,
    MatIconModule,
    FilePickerModule,
    MatMenuModule,
    MatDividerModule,
    MatFormFieldModule,
    FormsModule,
    MatInput,
    ReactiveFormsModule,
    MatSelectModule,
    StatusBadgeComponent
  ],
  providers: [DatePipe],
  templateUrl: './panal.component.html',
  styleUrl: './panal.component.scss'
})
export class PanalComponent implements OnInit, AfterViewInit {

  dtOptions: any = {};
  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
  @ViewChild('activeStatus') activeStatus: any;
  @ViewChild('btNg') btNg: any;
  @ViewChild(DataTableDirective, { static: false })
  dtElement: DataTableDirective;

  branches: any
  filterBranch: any
  constructor(
    public dialog: MatDialog,
    private datePipe: DatePipe,
    private panalservice: PanalService,
    private fuseConfirmationService: FuseConfirmationService,
    private toastr: ToastrService,
    private _router: Router,

  ) {
    this.panalservice.getBranchs().subscribe({
      next: (resp) => {
        this.branches = resp
      }
    })
  }

  ngOnInit(): void {
    setTimeout(() => {
      this.dtOptions = {
        serverSide: true,
        ajax: (dataTablesParameters: any, callback) => {
          dataTablesParameters.filter = {
            'filter.branch.id': this.filterBranch ?? '',
          }
          this.panalservice.datatable(dataTablesParameters).subscribe({
            next: (resp: any) => {
              callback({
                recordsTotal: resp.meta.totalItems,
                recordsFiltered: resp.meta.totalItems,
                data: resp.data
              });
            }, error: () => {
              this.toastr.error('เกิดข้อผิดพลาด')
            }
          })
        },
        columns: [
          {
            orderable: false,
            title: 'ลำดับ',
            data: 'no',
            className: 'w-15 text-center',
          },
          {
            title: 'ชื่อหน้าจอ',
            data: 'name',
            className: 'text-left',
          },
          {
            title: 'แสดง',
            data: null,
            defaultContent: '',
            ngTemplateRef: {
              ref: this.activeStatus,
            },
            className: 'w-30 text-center'
          },
          {
            title: 'จัดการ',
            data: null,
            defaultContent: '',
            ngTemplateRef: {
              ref: this.btNg,
            },
            className: 'w-15 text-center'
          }
        ],
        // columnDefs: [
        //   {
        //     orderable: false,
        //     className: 'select-checkbox',
        //     targets: 0
        //   }
        // ],
        // select: {
        //   style: 'multi',
        //   // selector: 'td:first-child'
        // },
        order: [[1, 'asc']]
      };
    });
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.dtTrigger.next(this.dtOptions);
    }, 200);
  }

  ngOnDestroy(): void {
    // Do not forget to unsubscribe the event
    this.dtTrigger.unsubscribe();
  }

  rerender(): void {
    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
      dtInstance.destroy();
      this.dtTrigger.next(this.dtOptions);
    });
  }

  onChangeBranch() {
    this.rerender()
  }

  opendialogapro() {
    this._router.navigate(['panel/form']);
  }

  openpanel() {
    console.log("Navigating to manage panel");
    this._router.navigate(['panel/managepanel']);
  }


  clickDelete(id: any) {
    const confirmation = this.fuseConfirmationService.open({
      title: "ยืนยันลบข้อมูล",
      message: "กรุณาตรวจสอบข้อมูล หากลบข้อมูลแล้วจะไม่สามารถนำกลับมาได้",
      icon: {
        show: true,
        name: "heroicons_outline:exclamation-triangle",
        color: "warn"
      },
      actions: {
        confirm: {
          show: true,
          label: "ยืนยัน",
          color: "warn"
        },
        cancel: {
          show: true,
          label: "ยกเลิก"
        }
      },
      dismissible: false
    })

    confirmation.afterClosed().subscribe(
      result => {
        if (result == 'confirmed') {
          this.panalservice.delete(id).subscribe({
            error: (err) => {

            },
            complete: () => {
              this.toastr.success('ดำเนินการลบสำเร็จ');

              this.rerender();
            },
          });
        }
      }
    )
  }

  openDialogEdit(data?: any) {
    this._router.navigate(['panel/edit/' + data.id])

  }
}
