<div class="flex flex-col flex-auto min-w-0">
    <div class="flex-auto sm:p-6 bg-white dark:bg-gray-800 shadow-lg m-4 p-6 rounded-xl">
        <div class="flex flex-row justify-between items-center pb-6 mb-6 border-b border-gray-200 dark:border-gray-700">
            <div>
                <h2 class="text-2xl md:text-3xl font-bold text-gray-800 dark:text-white">เงื่อนไขการขาย</h2>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">จัดการเงื่อนไข Mix & Match</p>
            </div>
            <div>
                <button mat-flat-button [color]="'primary'" (click)="openCreate()">
                    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:plus-circle'"></mat-icon>
                    <span class="ml-2 "> เพิ่ม Rule</span>
                </button>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="row-border hover w-full"></table>
        </div>
    </div>
</div>

<ng-template #btNg let-data="adtData">
    <div class="flex items-center justify-center gap-2">
        <button mat-icon-button color="primary" (click)="openEdit(data)">
            <mat-icon>edit</mat-icon>
        </button>
        <button mat-icon-button color="warn" (click)="clickDelete(data.id)">
            <mat-icon>delete</mat-icon>
        </button>
    </div>
</ng-template>
