<h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="data?.type === 'NEW'">เพิ่ม Mix & Match Rule</h1>
<h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="data?.type === 'EDIT'">แก้ไข Mix & Match Rule</h1>

<div mat-dialog-content class="overflow-y-auto">
  <form [formGroup]="form" class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <mat-form-field appearance="fill">
      <mat-label>รหัส</mat-label>
      <input matInput formControlName="code" placeholder="ระบุรหัส" />
    </mat-form-field>

    <mat-form-field appearance="fill">
      <mat-label>ชื่อ</mat-label>
      <input matInput formControlName="name" placeholder="ระบุชื่อ" />
    </mat-form-field>

    <mat-form-field appearance="fill">
      <mat-label>จำนวน</mat-label>
      <input matInput type="number" formControlName="stepQty" />
    </mat-form-field>

    <mat-form-field appearance="fill">
      <mat-label>ประเภทสิทธิ์</mat-label>
      <mat-select formControlName="benefitType">
        <mat-option *ngFor="let t of benefitTypes" [value]="t.value">{{t.label}}</mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field appearance="fill">
      <mat-label>ราคาที่ขาย</mat-label>
      <input matInput type="number" formControlName="benefitValue" />
    </mat-form-field>

    <mat-form-field appearance="fill">
      <mat-label>ลำดับความสำคัญ</mat-label>
      <input matInput type="number" formControlName="priority" />
    </mat-form-field>

    <mat-form-field appearance="fill">
      <mat-label>เริ่มวันที่</mat-label>
      <input matInput [matDatepicker]="startPicker" formControlName="startDate" />
      <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
      <mat-datepicker #startPicker></mat-datepicker>
    </mat-form-field>

    <mat-form-field appearance="fill">
      <mat-label>ถึงวันที่</mat-label>
      <input matInput [matDatepicker]="endPicker" formControlName="endDate" />
      <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
      <mat-datepicker #endPicker></mat-datepicker>
    </mat-form-field>

    <div class="col-span-1 md:col-span-2 flex items-center gap-6">
      <!-- <mat-slide-toggle color="primary" formControlName="combinable">รวมกับโปรอื่น</mat-slide-toggle> -->
      <mat-slide-toggle color="primary" formControlName="isActive">แสดง</mat-slide-toggle>
      <!-- <mat-form-field appearance="fill" class="w-48">
        <mat-label>Max sets / transaction</mat-label>
        <input matInput type="number" formControlName="maxSetsPerTxn" />
      </mat-form-field> -->
    </div>

    <div class="col-span-1 md:col-span-2">
      <label class="block font-semibold mb-2">สินค้าใน Rule</label>
      <div class="flex items-start gap-3">
        <app-dropdown-search-lazy
          class="flex-1"
          [items]="products"
          [config]="productConfig"
          [label]="'เลือกสินค้า'"
          (selected)="selectedProductId = $event?.id">
        </app-dropdown-search-lazy>
        <button mat-flat-button color="primary" type="button" (click)="addItem()">เพิ่ม</button>
      </div>
      <div class="mt-3 flex flex-wrap gap-2">
        <div *ngFor="let it of items; let i = index" class="px-2 py-1 bg-gray-100 rounded flex items-center gap-2">
          <span class="text-sm">{{it.code}} {{it.name}}</span>
          <button mat-icon-button color="warn" (click)="removeItem(i)"><mat-icon>close</mat-icon></button>
        </div>
      </div>
    </div>
  </form>
</div>

<div mat-dialog-actions class="flex justify-end">
  <button mat-stroked-button type="button" (click)="onClose()">ยกเลิก</button>
  <button mat-flat-button color="primary" type="button" (click)="onSubmit()">บันทึก</button>

</div>

