import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { FormsModule } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MixMatchRuleDto, MixMatchRuleService } from './mix-match-rule.service';
import { DropdownSearchConfig, DropdownSearchLazyComponent } from 'app/modules/common/dropdown-search-lazy/dropdown-search-lazy.component';
import { InventoryService } from '../inventory/inventory.service';

@Component({
  selector: 'app-mix-match-rule-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatDialogTitle,
    MatDialogContent,
    MatDialogActions,
    MatDialogClose,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatIconModule,
    MatDatepickerModule,
    DropdownSearchLazyComponent,
  ],
  templateUrl: './mix-match-rule.dialog.html',
  styleUrl: './mix-match-rule.dialog.scss'
})
export class DialogMixMatchRuleComponent implements OnInit {
  form: FormGroup;
  productConfig: DropdownSearchConfig = {
    valueKey: 'id',
    displayKeys: ['code', 'name', 'price'],
    searchKeys: ['code', 'name'],
    displaySeparator: ' '
  };
  products: any[] = [];
  selectedProductId: number | null = null;
  items: { productId: number, code?: string, name?: string }[] = [];

  benefitTypes = [
    { value: 'FIXED_TOTAL', label: 'ราคาคงที่' },
    // { value: 'FIXED_DISCOUNT', label: 'Fixed Discount' },
    // { value: 'PERCENT', label: 'Percent' },
  ];

  constructor(
    private dialogRef: MatDialogRef<DialogMixMatchRuleComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder,
    private toastr: ToastrService,
    private confirmation: FuseConfirmationService,
    private service: MixMatchRuleService,
    private inventoryService: InventoryService,
  ) {
    this.form = this.fb.group({
      code: ['', Validators.required],
      name: ['', Validators.required],
      stepQty: [2, [Validators.required, Validators.min(1)]],
      benefitType: ['FIXED_TOTAL', Validators.required],
      benefitValue: [0, [Validators.required, Validators.min(0)]],
      startDate: [null, Validators.required],
      endDate: [null, Validators.required],
      priority: [100, [Validators.required, Validators.min(0)]],
      combinable: [false],
      maxSetsPerTxn: [null],
      isActive: [true],
    });

    this.inventoryService.getProduct().subscribe((resp: any) => {
      this.products = resp;
    });
  }

  ngOnInit(): void {
    if (this.data?.type === 'EDIT' && this.data?.value) {
      const v = this.data.value;
      this.form.patchValue({
        code: v.code,
        name: v.name,
        stepQty: v.stepQty,
        benefitType: v.benefitType,
        benefitValue: v.benefitValue,
        startDate: v.startAt ? new Date(v.startAt) : null,
        endDate: v.endAt ? new Date(v.endAt) : null,
        priority: v.priority,
        combinable: v.combinable,
        maxSetsPerTxn: v.maxSetsPerTxn,
        isActive: v.isActive,
      });
      if (Array.isArray(v.items)) {
        this.items = v.items.map((it: any) => ({ productId: it?.product?.id, code: it?.product?.code, name: it?.product?.name }));
      }
    }
  }

  addItem() {
    if (!this.selectedProductId) return;
    const exists = this.items.some(i => i.productId === this.selectedProductId);
    if (exists) {
      this.toastr.info('มีสินค้าในรายการแล้ว');
      return;
    }
    const p = this.products.find((x: any) => x.id === this.selectedProductId);
    this.items.push({ productId: this.selectedProductId, code: p?.code, name: p?.name });
    this.selectedProductId = null;
  }

  removeItem(index: number) {
    this.items.splice(index, 1);
  }

  private toPayload(): MixMatchRuleDto {
    const v = this.form.value;
    const start = new Date(v.startDate);
    start.setHours(0, 0, 0, 0);
    const end = new Date(v.endDate);
    end.setHours(23, 59, 59, 0);

    return {
      code: v.code,
      name: v.name,
      stepQty: +v.stepQty,
      benefitType: v.benefitType,
      benefitValue: +v.benefitValue,
      startAt: start.toISOString(),
      endAt: end.toISOString(),
      priority: +v.priority,
      combinable: !!v.combinable,
      maxSetsPerTxn: +v.maxSetsPerTxn,
      isActive: !!v.isActive,
      items: this.items.map(i => ({ productId: i.productId }))
    } as MixMatchRuleDto;
  }

  onSubmit() {
    if (this.form.invalid || this.items.length === 0) {
      this.toastr.error('กรุณากรอกข้อมูลให้ครบถ้วน และเลือกสินค้าอย่างน้อย 1 รายการ');
      return;
    }

    const confirmation = this.confirmation.open({
      title: 'ยืนยันการบันทึกข้อมูล',
      icon: { show: true, name: 'heroicons_outline:exclamation-triangle', color: 'primary' },
      actions: {
        confirm: { show: true, label: 'ยืนยัน', color: 'primary' },
        cancel: { show: true, label: 'ยกเลิก' }
      },
      dismissible: false
    });

    confirmation.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        const payload = this.toPayload();
        if (this.data?.type === 'EDIT' && this.data?.value?.id) {
          this.service.update(this.data.value.id, payload).subscribe({
            next: () => this.toastr.success('แก้ไขข้อมูลสำเร็จ'),
            error: () => this.toastr.error('ไม่สามารถบันทึกข้อมูลได้'),
            complete: () => this.dialogRef.close(true)
          });
        } else {
          this.service.create(payload).subscribe({
            next: () => this.toastr.success('เพิ่มข้อมูลสำเร็จ'),
            error: () => this.toastr.error('ไม่สามารถบันทึกข้อมูลได้'),
            complete: () => this.dialogRef.close(true)
          });
        }
      }
    });
  }

  onClose() {
    this.dialogRef.close();
  }
}

