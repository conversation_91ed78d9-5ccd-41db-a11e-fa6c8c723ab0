import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';
import { toUpper } from 'lodash';
import { map } from 'rxjs';

export interface MixMatchItem {
  productId: number;
}

export interface MixMatchRuleDto {
  code: string;
  name: string;
  stepQty: number;
  benefitType: 'FIXED_TOTAL' | 'PERCENT' | 'FIXED_DISCOUNT';
  benefitValue: number;
  startAt: string; // ISO string
  endAt: string;   // ISO string
  priority: number;
  combinable: boolean;
  maxSetsPerTxn: number;
  isActive: boolean;
  items: MixMatchItem[];
}

@Injectable({ providedIn: 'root' })
export class MixMatchRuleService {

  constructor(private http: HttpClient) {}

  datatable(dataTablesParameters: any) {
    const { columns, order, search, start, length } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;

    return this.http
      .get(`${environment.apiUrl}/api/mix-and-match-rule/datatables`, {
        params: {
          page: page,
          limit: length,
          sortBy: sortBy,
          search: search?.value ?? '',
        },
      })
      .pipe(
        map((resp: any) => {
          resp.data.forEach((e: any, i: number) => (e.no = start + i + 1));
          return resp;
        })
      );
  }

  list() {
    return this.http.get(`${environment.apiUrl}/api/mix-and-match-rule`);
  }

  getById(id: number) {
    return this.http.get(`${environment.apiUrl}/api/mix-and-match-rule/${id}`);
  }

  create(data: MixMatchRuleDto) {
    return this.http.post(`${environment.apiUrl}/api/mix-and-match-rule`, data);
  }

  update(id: number, data: Partial<MixMatchRuleDto>) {
    return this.http.put(`${environment.apiUrl}/api/mix-and-match-rule/${id}`, data);
  }

  delete(id: number) {
    return this.http.delete(`${environment.apiUrl}/api/mix-and-match-rule/${id}`);
  }
}

