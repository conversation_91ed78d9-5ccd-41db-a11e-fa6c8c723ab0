import { CommonModule, DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, OnInit, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { MixMatchRuleService } from './mix-match-rule.service';
import { DialogMixMatchRuleComponent } from './mix-match-rule.dialog';

@Component({
    selector: 'app-mix-match-rule-list',
    standalone: true,
    imports: [
        CommonModule,
        DataTablesModule,
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
        MatDividerModule,
    ],
    templateUrl: './mix-match-rule.component.html',
    styleUrl: './mix-match-rule.component.scss',
    changeDetection: ChangeDetectionStrategy.Default,
    providers: [DatePipe]
})
export class MixMatchRuleListComponent implements OnInit, AfterViewInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();

    @ViewChild('btNg') btNg: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;

    constructor(
        private service: MixMatchRuleService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        private dialog: MatDialog,
    ) { }

    ngOnInit(): void {
        setTimeout(() => this.loadTable());
    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        this.dtTrigger.unsubscribe();
    }

    loadTable(): void {
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,
            ajax: (dataTablesParameters: any, callback) => {
                this.service.datatable(dataTablesParameters).subscribe({
                    next: (resp: any) => {
                        callback({
                            recordsTotal: resp.meta.totalItems,
                            recordsFiltered: resp.meta.totalItems,
                            data: resp.data,
                        });
                    },
                    error: () => {
                        this.toastr.error('เกิดข้อผิดพลาด');
                    },
                });
            },
            columns: [
                { title: 'ลำดับ', data: 'no', className: 'w-15 text-center' },
                { title: 'รหัส', data: 'code', className: 'text-center' },
                { title: 'ชื่อ', data: 'name', className: 'text-center' },
                { title: 'จำนวน', data: 'stepQty', className: 'text-center' },
                { title: 'ประเภทสิทธิ์', data: 'benefitType', className: 'text-center' },
                { title: 'ราคาที่ขาย', data: 'benefitValue', className: 'text-center' },
                { title: 'ลำดับความสำคัญ', data: 'priority', className: 'text-center' },
                { title: 'รวมกับโปรอื่น', data: 'combinable', render: (d: any) => d ? 'ใช่' : 'ไม่', className: 'text-center' },
                { title: 'แสดง', data: 'isActive', render: (d: any) => d ? 'แสดง' : 'ซ่อน', className: 'text-center' },
                {
                    title: 'จัดการ',
                    data: null,
                    defaultContent: '',
                    ngTemplateRef: { ref: this.btNg },
                    className: 'w-15 text-center',
                },
            ],
        };
    }

    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            dtInstance.destroy();
            this.dtTrigger.next(this.dtOptions);
        });
    }

    openCreate(): void {
        const dialogRef = this.dialog.open(DialogMixMatchRuleComponent, {
            disableClose: true,
            width: '720px',
            height: 'auto',
            enterAnimationDuration: 300,
            exitAnimationDuration: 300,
            data: { type: 'NEW' }
        });
        dialogRef.afterClosed().subscribe((result) => {
            if (result) this.rerender();
        });
    }

    openEdit(item: any): void {
        this.service.getById(item.id).subscribe((resp: any) => {
            const dialogRef = this.dialog.open(DialogMixMatchRuleComponent, {
                disableClose: true,
                width: '720px',
                height: '90%',
                enterAnimationDuration: 300,
                exitAnimationDuration: 300,
                data: { type: 'EDIT', value: resp }
            });
            dialogRef.afterClosed().subscribe((result) => {
                if (result) this.rerender();
            });
        });

    }

    clickDelete(id: any) {
        const confirmation = this.fuseConfirmationService.open({
            title: 'ยืนยันลบข้อมูล',
            message: 'กรุณาตรวจสอบข้อมูล หากลบข้อมูลแล้วจะไม่สามารถนำกลับมาได้',
            icon: { show: true, name: 'heroicons_outline:exclamation-triangle', color: 'warn' },
            actions: {
                confirm: { show: true, label: 'ยืนยัน', color: 'primary' },
                cancel: { show: true, label: 'ยกเลิก' },
            },
            dismissible: false,
        });

        confirmation.afterClosed().subscribe((result) => {
            if (result === 'confirmed') {
                this.service.delete(id).subscribe({
                    next: () => this.toastr.success('ดำเนินการลบสำเร็จ'),
                    error: () => this.toastr.error('เกิดข้อผิดพลาด'),
                    complete: () => this.rerender(),
                });
            }
        });
    }
}

