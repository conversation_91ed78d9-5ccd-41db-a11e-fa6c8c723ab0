import { Injectable, signal } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { map, switchMap, tap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  
  dashboardData = signal<any>(null);

  constructor(private http: HttpClient) {}

  getBranchNames(): Observable<string[]> {
    return this.http.get<any[]>('/api/branch').pipe( );
  }

  getBranch(): Observable<string[]> {
    return this.http.get<any[]>('/api/branch')
  }

  getDashboard(): Observable<any> {
    const branchId = Number(localStorage.getItem('branch'));
    return this.http.get<any>('/api/dashboard', { params: { branchId:branchId } }).pipe();
  }

  getDashboardOverview<T>(data: any): Observable<T> {
    const branchId = Number(localStorage.getItem('branch'));

    return this.http.get<T>('/api/dashboard/overview', { params: { branchId: branchId, startDate: data.startDate, endDate: data.endDate } })
      // .pipe(
      //   switchMap((resp: any) => {
      //     this.dashboardData.set(resp);
      //     return resp;
      //   })
      // );
  //}
  }
}
