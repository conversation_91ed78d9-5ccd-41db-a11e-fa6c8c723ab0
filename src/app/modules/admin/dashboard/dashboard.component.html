<!-- Main Content -->
<div class="flex-1 overflow-auto">
  <!-- Header -->
  <header class="bg-white shadow-sm p-4 flex justify-between items-center">
    <div class="flex items-center space-x-2">
      <i class="fas fa-bars text-blue-800 md:hidden"></i>
      <h1 class="text-xl font-bold text-gray-800">ภาพรวมระบบ POS</h1>
    </div>
    <div class="flex items-center space-x-4">
      <!-- <div class="relative">
        <i class="fas fa-bell text-gray-600"></i>
        <span
          class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
      </div>
      <div class="flex items-center space-x-2">
        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Profile" class="w-8 h-8 rounded-full">
        <span class="hidden md:inline">ผู้จัดการ สาขา</span>
      </div> -->
    </div>
  </header>

  <!-- Dashboard Content -->
  <div class="p-6 space-y-6">
    <!-- Cards Overview -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Stock Overview -->
      <div class="bg-white shadow rounded-lg p-4">
        <div class="flex justify-between items-start">
          <div>
            <p class="text-sm text-gray-500">สินค้าคงเหลือทั้งหมด</p>
            <p class="text-2xl font-bold text-blue-600">{{dashboardData()?.productRemaining | number:'1.0-0'}} รายการ
            </p>
          </div>
          <i class="fas fa-boxes text-blue-200 text-xl"></i>
        </div>
        <!-- <div class="mt-4 text-xs space-y-1">
          <div class="flex justify-between">
            <span class="text-amber-600">ใกล้หมด (<5 ชิ้น)</span>
                <span>24 รายการ</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500">ไม่เคลื่อนไหว (30 วัน)</span>
            <span>56 รายการ</span>
          </div>
        </div> -->
      </div>

      <!-- Today's Sales -->
      <div class="bg-white shadow rounded-lg p-4">
        <div class="flex justify-between items-start">
          <div>
            <p class="text-sm text-gray-500">ยอดขายวันนี้</p>
            <p class="text-2xl font-bold text-green-600">{{dashboardData()?.sales.sales | number:'1.2-2'}} บาท</p>
            <!-- <p class="text-xs text-gray-400">กะปัจจุบัน: 08:00 - 17:00</p> -->
          </div>
          <i class="fas fa-shopping-cart text-green-200 text-xl"></i>
        </div>
        <!-- <div class="mt-4 text-xs space-y-1">
          <div class="flex justify-between">
            <span>จำนวนบิล</span>
            <span class="font-medium">{{dashboardData()?.sales.orders | number:'1.0-0'}} บิล</span>
          </div>
          <div class="flex justify-between">
            <span>เฉลี่ยต่อบิล</span>
            <span class="font-medium">{{dashboardData()?.sales.sales / dashboardData()?.sales.orders |
              number:'1.0-0'}}</span>
          </div>
        </div> -->
      </div>

            <!-- Total Bill -->
      <div class="bg-white shadow rounded-lg p-4">
        <div class="flex justify-between items-start">
          <div>
            <p class="text-sm text-gray-500">จำนวนบิล</p>
            <p class="text-2xl font-bold text-indigo-600">{{dashboardData()?.sales.orders | number:'1.0-0'}} บิล</p>
            <!-- <p class="text-xs text-gray-400">กะปัจจุบัน: 08:00 - 17:00</p> -->
          </div>
          <i class="fas fa-shopping-cart text-green-200 text-xl"></i>
        </div>
        <!-- <div class="mt-4 text-xs space-y-1">
          <div class="flex justify-between">
            <span>จำนวนบิล</span>
            <span class="font-medium">{{dashboardData()?.sales.orders | number:'1.0-0'}} บิล</span>
          </div>
          <div class="flex justify-between">
            <span>เฉลี่ยต่อบิล</span>
            <span class="font-medium">{{dashboardData()?.sales.sales / dashboardData()?.sales.orders |
              number:'1.0-0'}}</span>
          </div>
        </div> -->
      </div>


      <!-- Profit/Loss -->
      <div class="bg-white shadow rounded-lg p-4">
        <div class="flex justify-between items-start">
          <div>
            <p class="text-sm text-gray-500">กำไรวันนี้</p>
            <p class="text-2xl font-bold text-rose-600">{{dashboardData()?.profit | number:'1.0-0'}} บาท</p>
            <!-- <p class="text-xs text-gray-400">กำไรสุทธิ</p> -->
          </div>
          <i class="fas fa-chart-pie text-rose-200 text-xl"></i>
        </div>
        <!-- <div class="mt-4 text-xs space-y-1">
          <div class="flex justify-between">
            <span>ต้นทุนรวม</span>
            <span class="font-medium">{{dashboardData()?.profit | number:'1.0-0'}}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-green-500">+5.2%</span>
            <span>เทียบกับเมื่อวาน</span>
          </div>
        </div> -->
      </div>
    </div>

    <!-- Graph & Tables -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Sales Graph -->
      <div class="col-span-2 bg-white rounded-lg shadow p-4">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">ยอดขาย 7 วันล่าสุด</h2>
          <!-- <div class="flex space-x-2">
            <button class="px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">รายวัน</button>
            <button class="px-3 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">รายสัปดาห์</button>
            <button class="px-3 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">รายเดือน</button>
          </div> -->
        </div>
        <apx-chart [series]="chartOptions.series" [chart]="chartOptions.chart" [xaxis]="chartOptions.xaxis"
          [stroke]="chartOptions.stroke" [dataLabels]="chartOptions.dataLabels" [title]="chartOptions.title"
          [grid]="chartOptions.grid">
        </apx-chart>
      </div>

      <!-- Best Sellers -->
      <div class="bg-white rounded-lg shadow p-4">
        <h2 class="text-lg font-semibold mb-4">สินค้าขายดี (Top 5)</h2>
        @if (dashboardData()?.top5Products.length > 0) {
        <ul class="divide-y divide-gray-200 text-sm">
          @for (item of dashboardData()?.top5Products; track $index) {
          <li class="py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
              <span
                class="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-xs">{{$index
                + 1}}</span>
              <span>{{item.name}}</span>
            </div>
            <div class="text-right">
              <span class="block font-medium">{{item.quantity | number:'1.0-0'}}
              </span>
              <span class="text-xs text-gray-500">{{item.totalSales | number:'1.0-0'}} บาท</span>
            </div>
          </li>
          }
        </ul>
        }
        @else {
          <p class="text-sm text-gray-500 text-center w-full">ไม่มีข้อมูล</p>
        }
      </div>
    </div>
  </div>
</div>