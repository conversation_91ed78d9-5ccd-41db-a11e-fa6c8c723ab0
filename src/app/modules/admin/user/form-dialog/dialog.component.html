<div class="md:max-w-lg">
    <ng-container *ngIf="this.data?.type === 'CHANGE'">
        <h1 mat-dialog-title class="mb-6 text-xl font-semibold">เปลี่ยนรหัสผ่าน</h1>
        <div mat-dialog-content class="h-fit">
            <form [formGroup]="form">
                <div class="flex-auto">
                    <div class="flex flex-col md:flex">
                        <div class="md:w-full pb-4">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                <mat-label class="pb-1">New Password</mat-label>
                                <input id="password" matInput type="password" [formControlName]="'newPassword'" #passwordField required pattern="^(?=.*[A-Z])(?=.*[0-9])(?=.*[-+_!@#$%^&*,.?])(?=.*[a-z]).{8,}$">

                                <button mat-icon-button type="button"
                                    (click)="passwordField.type === 'password' ? passwordField.type = 'text' : passwordField.type = 'password'"
                                    matSuffix>
                                    <mat-icon class="icon-size-5" *ngIf="passwordField.type === 'password'"
                                        [svgIcon]="'heroicons_solid:eye'"></mat-icon>
                                    <mat-icon class="icon-size-5" *ngIf="passwordField.type === 'text'"
                                        [svgIcon]="'heroicons_solid:eye-slash'"></mat-icon>
                                </button>
                                <!-- <mat-error class="text-xs">Password must be a combination of lower-case, upper-case, numbers, symbols and at least 8 characters long
                                </mat-error> -->
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <!-- <div> check : {{this.form.value | json}}</div> -->
        <div mat-dialog-actions class="flex justify-end">
            <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
                ตกลง
            </button>
            <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
                ยกเลิก
            </button>
        </div>
    </ng-container>
    <ng-container *ngIf="this.data?.type !== 'CHANGE'">
        <h1 mat-dialog-title class="mb-4 text-xl font-semibold" *ngIf="this.data?.type === 'NEW'">เพิ่มข้อมูล</h1>
        <h1 mat-dialog-title class="mb-4 text-xl font-semibold" *ngIf="this.data?.type === 'EDIT'">แก้ไขข้อมูล</h1>
        <div mat-dialog-content class="h-fit">
            <form [formGroup]="form">
                <div class="flex-auto">
                    <div class="flex flex-col mb-2 md:flex">
                        <div class="md:w-full mb-1">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                <mat-label>รหัสพนักงาน</mat-label>
                                <input matInput [placeholder]="'กรุณาระบุรหัสพนักงาน'" formControlName="code"
                                    [readonly]="this.data.type === 'EDIT'">
                                <mat-error class="text-xs">กรุณาระบุรหัสพนักงาน</mat-error>
                            </mat-form-field>
                        </div>
                        <div class="md:w-full mb-1">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                <mat-label>ชื่อ</mat-label>
                                <input matInput [placeholder]="'กรุณาระบุชื่อพนักงาน'" formControlName="firstName">
                                <mat-error class="text-xs">กรุณาระบุชื่อพนักงาน</mat-error>
                            </mat-form-field>
                        </div>
                        <div class="md:w-full mb-1">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                <mat-label>นามสกุล</mat-label>
                                <input matInput [placeholder]="'กรุณาระบุนามสกุลพนักงาน'" formControlName="lastName">
                                <mat-error class="text-xs">กรุณาระบุนามสกุลพนักงาน</mat-error>
                            </mat-form-field>
                        </div>
                        <div class="md:w-full mb-1">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                <mat-label>เบอร์ติดต่อ</mat-label>
                                <input matInput [placeholder]="'กรุณาระบุเบอร์ติดต่อ'" formControlName="phoneNumber"
                                    type="tel">
                                <mat-error class="text-xs">กรุณาระบุเบอร์ติดต่อ</mat-error>
                            </mat-form-field>
                        </div>

                        <div class="md:w-full mb-1">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                <mat-label>สิทธิ์การใช้งาน</mat-label>
                                <mat-select  formControlName="roleId">
                                    <mat-option value="">
                                        เลือกสิทธิ์การใช้งาน
                                    </mat-option>
                                    <mat-option *ngFor="let item of roles;" [value]="item.id">
                                        {{item.name}}
                                    </mat-option>
                                </mat-select>
                                <mat-error class="text-xs">กรุณาเลือกสิทธิ์การใช้งาน</mat-error>
                            </mat-form-field>
                        </div>

                        <div class="md:w-full mb-1">
                        <!-- <form [formGroup]="form" (ngSubmit)="Submit()"> -->
                            <mat-form-field [ngClass]="formFieldHelpers" appearance="fill" class="w-full">
                                <mat-label>Select Branches</mat-label>
                                <mat-select formControlName="branchIds" multiple (selectionChange)="selectionChanged($event)">
                                    <mat-option [disabled]="true" value="">เลือกสาขา</mat-option>
                                    <mat-option *ngFor="let branch of branches" [value]="branch.id">{{ branch.name }}</mat-option>
                                </mat-select>
                                <mat-error class="text-xs">กรุณาเลือกสาขาอย่างน้อย 1 สาขา</mat-error>
                            </mat-form-field>
                            <!-- </form> -->
                        </div>

                        <div class="md:w-full mb-1">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                <mat-label>Username</mat-label>
                                <input matInput [placeholder]="''" formControlName="username" >
                                <mat-error class="text-xs">กรุณาระบุยูสเซอร์เนม</mat-error>
                            </mat-form-field>
                        </div>
                        <div class="md:w-full pb-5" *ngIf="this.data.type === 'NEW'">
                            <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                                <mat-label>Password</mat-label>
                                <input id="password" matInput type="password" [formControlName]="'password'" #passwordField required pattern="^(?=.*[A-Z])(?=.*[0-9])(?=.*[-+_!@#$%^&*,.?])(?=.*[a-z]).{8,}$">

                                <button mat-icon-button type="button"
                                    (click)="passwordField.type === 'password' ? passwordField.type = 'text' : passwordField.type = 'password'"
                                    matSuffix>
                                    <mat-icon class="icon-size-5" *ngIf="passwordField.type === 'password'"
                                        [svgIcon]="'heroicons_solid:eye'"></mat-icon>
                                    <mat-icon class="icon-size-5" *ngIf="passwordField.type === 'text'"
                                        [svgIcon]="'heroicons_solid:eye-slash'"></mat-icon>
                                </button>
                                <mat-error class="text-xs">Password must be a combination of lower-case, upper-case, numbers, symbols and at least 8 characters long
                                </mat-error>
                            </mat-form-field>
                            <!--<mat-error class="text-xs">Password must be a combination of lower-case, upper-case, numbers, symbols and at least 8 characters long.</mat-error>-->
                        </div>
                        <!--<div class="md:w-full" *ngIf="this.data.type === 'EDIT'">
                            <mat-radio-group aria-label="Select an option" formControlName="isActive">
                                <mat-radio-button [value]="true" [color]="'primary'">เปิดใช้งาน</mat-radio-button>
                                <mat-radio-button [value]="false" [color]="'primary'">ปิดใช้งาน</mat-radio-button>
                            </mat-radio-group>
                        </div>-->
                        <div class="md:w-full">
                            <mat-slide-toggle formControlName="isActive" color="primary">แสดง</mat-slide-toggle>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <!-- <div> check : {{this.form.value | json}}</div> -->
        <div mat-dialog-actions class="flex justify-end">
            <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
                ตกลง
            </button>
            <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
                ยกเลิก
            </button>
        </div>
    </ng-container>
</div>
