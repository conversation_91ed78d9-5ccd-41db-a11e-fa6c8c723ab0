import { Subscription } from 'rxjs';
import { Component, OnInit, OnChanges, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataTablesModule } from 'angular-datatables';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import {
    MatDialog,
    MatDialogTitle,
    MatDialogContent,
    MatDialogActions,
    MatDialogClose,
    MatDialogRef,
    MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { AbstractControl, FormBuilder, FormControl, FormGroup, FormsModule, ValidatorFn, Validators } from '@angular/forms';
import { MatToolbarModule } from '@angular/material/toolbar';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { UserService } from '../user.service';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { ToastrService } from 'ngx-toastr';
import {MatRadioModule} from '@angular/material/radio';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { AuthService } from 'app/core/auth/auth.service';

@Component({
    selector: 'app-user-form',
    standalone: true,
    templateUrl: './dialog.component.html',
    styleUrl: './dialog.component.scss',
    imports: [CommonModule, DataTablesModule, MatIconModule, MatFormFieldModule, MatInputModule,
        FormsModule, MatToolbarModule,
        MatButtonModule,
        MatDialogTitle,
        MatDialogContent,
        MatDialogActions,
        MatDialogClose,
        MatSelectModule,
        ReactiveFormsModule,
        MatInputModule,
        MatFormFieldModule,
        MatRadioModule,
        MatSlideToggleModule,
    ]
})
export class DialogForm implements OnInit {
    passwordField: any
    form: FormGroup;
    stores: any[]=[];
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    dtOptions: DataTables.Settings = {};
    addForm: FormGroup;
    branches: any[] = [];
    selectedBranches: any[] = [];
    roles: any[] = [];
    //    { id: 2, name: 'Admin'},
    //    { id: 3, name: 'Supervisor'},
    //    { id: 5, name: 'Manager '},
    //    { id: 4, name: 'Cashier'},
    //    { id: 6, name: 'Office'},
    //    { id: 7, name: 'Finance'}
    // ];
     registerForm = new FormGroup({
        password: new FormControl('', [Validators.required]),
      });


    constructor(
        private dialogRef: MatDialogRef<DialogForm>,
        @Inject(MAT_DIALOG_DATA) public data: any,
        public dialog: MatDialog,
        private FormBuilder: FormBuilder,
        public _service: UserService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        private authService: AuthService,
    )
    {
        console.log(' this.form', this.data); // roles
        this._service.getRole().subscribe({
            next: (resp: any)=>{
                //console.log('resp',resp);
                this.roles = resp
            },error: () => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
        if(this.data.type === 'CHANGE') {
            this.form = this.FormBuilder.group({
                userId: this.data.value.id ?? '',
                newPassword: '',
            });
        }
        else if(this.data.type === 'EDIT') {
            this.form = this.FormBuilder.group({
                code: [this.data.value.code ?? '',[Validators.required]],
                firstName: [this.data.value.firstName ?? '',[Validators.required]],
                lastName: [this.data.value.lastName ?? '',[Validators.required]],
                phoneNumber: [this.data.value.phoneNumber ?? '',[Validators.required]],
                roleId: [this.data.value?.role?.id ?? '',[Validators.required]],
                username: [this.data.value?.username ?? '',[Validators.required]],
                isActive: [this.data.value?.isActive,[Validators.required]],
                branchIds: [this.data.value?.branches?.map((branch: any) => branch.id) ?? [''],[this.validateNotEmptyArray(), Validators.required]],
                storeId: [this.data.value?.store?.id ?? '']
             });
        } else {
            this.form = this.FormBuilder.group({
                code: ['',[Validators.required]],
                firstName: ['',[Validators.required]],
                lastName: ['',[Validators.required]],
                phoneNumber: ['',[Validators.required]],
                roleId: ['',[Validators.required]],
                username: ['',[Validators.required]],
                password: ['',[Validators.required]],
                confirmpassword: ['',[Validators.required]],
                isActive: [true,[Validators.required]],
                branchIds: [[''],[this.validateNotEmptyArray(),Validators.required]],
                storeId: [this.authService?.currentShop ?? '']
             });
        }
        // console.log('1111',this.data?.type);
    }


    ngOnInit(): void {
        console.log('this.passwordField',this.passwordField);

        this._service.getBranch().subscribe(() => {
            this._service.branches$.subscribe((branches) => {
              this.branches = branches;
            });
          });
        if (this.data.type === 'EDIT') {
        //   this.form.patchValue({
        //     ...this.data.value,
        //     roleId: +this.data.value?.role?.id
        //   })

        } else {
            console.log('New');
        }
    }


    Submit() {
        let formValue = this.form.value
        if(this.data.type!="CHANGE"){
            formValue.branchIds = formValue.branchIds.filter(item => item !== '');
            if (formValue.branchIds.length == 0){
                formValue.branchIds = ''
            }
        }
        const confirmation = this.fuseConfirmationService.open({
            title: "ยืนยันการบันทึกข้อมูล",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "primary"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "ยืนยัน",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "ยกเลิก"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    if (this.data.type === 'CHANGE') {
                        this._service.forceChangePassword(formValue).subscribe({
                            error: (err) => {
                                this.toastr.error('ไม่สามารถบันทึกข้อมูลได้')
                            },
                            complete: () => {
                                this.toastr.success('ดำเนินการเปลี่ยนรหัสผ่านสำเร็จ')
                                this.dialogRef.close(true)
                            },
                        });
                    } else if (this.data.type === 'NEW') {
                        this._service.create(formValue).subscribe({
                            error: (err) => {
                                this.toastr.error('ไม่สามารถบันทึกข้อมูลได้')
                            },
                            complete: () => {
                                this.toastr.success('ดำเนินการเพิ่มข้อมูลสำเร็จ')
                                this.dialogRef.close(true)
                            },
                        });
                    } else {
                        this._service.update(this.data.value.id ,formValue).subscribe({
                            error: (err) => {
                                this.toastr.error('ไม่สามารถบันทึกข้อมูลได้')
                            },
                            complete: () => {
                                this.toastr.success('ดำเนินการแก้ไขข้อมูลสำเร็จ')
                                this.dialogRef.close(true)
                            },
                        });
                    }
                }
            }
        )
    }

    onClose() {
        this.dialogRef.close()
    }

    validateNotEmptyArray(): ValidatorFn {
        return (control: AbstractControl): { [key: string]: any } | null => {
          if (control.value.length === 1 && control.value[0] === '') {
            return { 'invalidBranchSelection': true };
          }
          return null;
        };
    }

    selectionChanged(event: any) {
        // Map selected branches to their IDs
        this.selectedBranches = event.value.filter(item => item !== '');
        if (this.selectedBranches.length == 0){
            this.selectedBranches = ['']
        }
        this.form.patchValue({
            branchIds: this.selectedBranches
        });
        console.log('Selected Branch IDs:', this.form.get('branchIds')?.value);
    }
}
