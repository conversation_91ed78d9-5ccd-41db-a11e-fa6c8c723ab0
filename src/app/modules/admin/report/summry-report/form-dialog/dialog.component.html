<div class="md:max-w-lg">
    <h1 mat-dialog-title class="mb-4 text-xl font-semibold" *ngIf="this.data?.type === 'NEW'">ค้นหาข้อมูล</h1>
    <h1 mat-dialog-title class="mb-4 text-xl font-semibold" *ngIf="this.data?.type === 'EDIT'">แก้ไขข้อมูล</h1>
    <div mat-dialog-content class="overflow-y-auto md:max-h-180">
        <form [formGroup]="form">
            <div class="flex-auto">
                <div class="flex flex-col mb-6 md:flex">

                    <div class="md:w-full">
                        <mat-form-field class="flex w-full" >
                            <mat-date-range-input [formGroup]="form" [rangePicker]="picker">
                                <input matStartDate placeholder="วันที่เริ่ม" formControlName="startDate">
                                <input matEndDate placeholder="วันที่สิ้นสุด" formControlName="endDate">
                            </mat-date-range-input>
                            <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                            <mat-date-range-picker #picker></mat-date-range-picker>
                        </mat-form-field>
                    </div>

                </div>
            </div>
        </form>
    </div>
    <!-- <div> check : {{this.form.value | json}}</div> -->
    <div mat-dialog-actions class="flex justify-end">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
            ตกลง
        </button>
        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
            ยกเลิก
        </button>
    </div>
</div>
