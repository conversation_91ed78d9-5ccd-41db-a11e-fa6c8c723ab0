<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
    <div class="flex flex-row justify-between mb-2">
      <div>
        <h2 class="text-2xl md:text-xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
          รายงาน
        </h2>
     
      </div>
      <div class="w-50">
        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
          <!-- <mat-label>Search Report</mat-label> -->
          <input matInput [(ngModel)]="searchQuery" placeholder="ค้นหา" (keyup)="onKeyup()">
        </mat-form-field>
      </div>
    </div>
<div class="relative overflow-x-auto shadow-md sm:rounded-lg">
  <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
      <thead class="text-md text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
          <tr>
              <th scope="col" class="px-6 py-3 w-1/12">
                  No.
              </th>
              <th scope="col" class="px-6 py-3 w-9/12">
                ชื่อรายงาน
              </th>
        
              <th scope="col" class="px-6 py-3 w-2/12">
                  <span class="sr-only">Edit</span>
              </th>
          </tr>
      </thead>
      <tbody>
          <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700" *ngFor="let item of filteredReports ; let i = index">
              <th scope="row" class="px-6 py-4 text-md text-gray-900 whitespace-nowrap dark:text-white">
                  {{i + 1}}.
              </th>
              <td class="px-6 py-4">
                  {{item.name ?? '-'}}
              </td>
              <td class="px-6 py-4 text-right">
                  <a  class="font-medium text-blue-600 dark:text-blue-500 hover:underline cursor-pointer" (click)="openDialogReport(item)">Download</a>
              </td>
          </tr>
      </tbody>
  </table>
</div>

  </div>
</div>

<ng-template #btNg let-data="adtData">
  <button mat-icon-button [color]="'primary'" [matMenuTriggerFor]="menu">
    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cog-8-tooth'"></mat-icon>
  </button>
  <mat-menu #menu="matMenu">
    <button mat-menu-item (click)="openDialogEdit(data)">
      <mat-icon [svgIcon]="'heroicons_solid:pencil-square'"></mat-icon>
      <span>แก้ไข</span>
    </button>
    <mat-divider></mat-divider>
    <button mat-menu-item (click)="clickDelete(data.id)">
      <mat-icon [svgIcon]="'heroicons_solid:trash'"></mat-icon>
      <span>ลบ</span>
    </button>
  </mat-menu>
</ng-template>
