<div class="md:max-w-lg" >
    <h1 mat-dialog-title class="text-xl font-semibold mb-4" >{{data?.value.name}}</h1>
    <div mat-dialog-content class="overflow-y-auto md:max-h-180">
        <form [formGroup]="form">
            <div class="flex-auto">
                <ng-container *ngFor="let item of data.value.type; let i = index">
                    <div class="md:w-full" *ngIf="item === 'date'">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <input matInput [matDatepicker]="pickerToday" formControlName="date" placeholder="วันที่">
                            <mat-datepicker-toggle matSuffix [for]="pickerToday"></mat-datepicker-toggle>
                            <mat-datepicker #pickerToday></mat-datepicker>
                          </mat-form-field>
                    </div>
                    <div class="md:w-full" *ngIf="item === 'date-rang'">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-date-range-input  [rangePicker]="picker">
                              <input matStartDate formControlName="startDate" placeholder="วันที่เริ่มต้น">
                              <input matEndDate formControlName="endDate" placeholder="วันที่สิ้นสุด">
                            </mat-date-range-input>
                            <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                            <mat-date-range-picker #picker></mat-date-range-picker>
                          </mat-form-field>
                    </div>
                    <div class="md:w-full" *ngIf="item === 'wallet-type'">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>ประเภทกระเป๋าเงิน</mat-label>
                            <mat-select [formControlName]="'walletType'">
                                <mat-option *ngFor="let wallet of walletTypeData" [value]="wallet.key">
                                    {{wallet.name}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div class="md:w-full" *ngIf="item === 'card-type'">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>ประเภทบัตรพนักงาน</mat-label>
                            <mat-select [formControlName]="'cardType'">
                                <mat-option *ngFor="let card of cardTypeData" [value]="card">
                                    {{card}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div class="md:w-full" *ngIf="item === 'food-type'">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>ประเภทบุฟเฟ่</mat-label>
                            <mat-select [formControlName]="'foodType'">
                                <mat-option value="">All</mat-option>
                                <mat-option *ngFor="let food of foodTypeData" [value]="food">
                                    {{food}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div class="md:w-full" *ngIf="item === 'device-id'">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>อุปกรณ์</mat-label>
                            <mat-select [formControlName]="'deviceId'">
                                <mat-option [disabled]="true" value="">เลือกอุปกรณ์</mat-option>
                                <mat-option *ngFor="let device of deviceData" [value]="device.id">
                                    {{device.name}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div class="md:w-full" *ngIf="item === 'device-multi'">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>อุปกรณ์</mat-label>
                            <mat-select [formControlName]="'deviceIds'" multiple (selectionChange)="selectionDeviceChanged($event.value)">
                                <div class="px-3 pb-1 w-full">
                                    <input class="px-3 py-2 border-[1px] w-full rounded-[8px]" (input)="searchDeviceChange($event)" placeholder="Search"/>
                                </div>
                                <mat-option [disabled]="true" value="">เลือกอุปกรณ์</mat-option>
                                <mat-option value="all">เลือกอุปกรณ์ทั้งหมด</mat-option>
                                <mat-option *ngFor="let device of deviceData" [value]="device.id">
                                    {{device.name}}
                                </mat-option>
                            </mat-select>
                            <mat-error class="text-xs">กรุณาเลือกอุปกรณ์อย่างน้อย 1 อุปกรณ์</mat-error>
                        </mat-form-field>
                    </div>
                    <div class="md:w-full" *ngIf="item === 'member-multi'">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>สมาชิก</mat-label>
                            <mat-select [formControlName]="'memberIds'" multiple (selectionChange)="selectionMemberChanged($event.value)">
                                <div class="px-3 pb-1 w-full">
                                    <input class="px-3 py-2 border-[1px] w-full rounded-[8px]" (input)="searchMemberChange($event)" placeholder="Search"/>
                                </div>
                                <mat-option [disabled]="true" value="">เลือกสมาชิก</mat-option>
                                <mat-option value="all">เลือกสมาชิกทั้งหมด</mat-option>
                                <mat-option *ngFor="let member of memberData" [value]="member.id">
                                    {{member.firstname}} {{member.middlename ?? ''}} {{member.lastname}}
                                </mat-option>
                            </mat-select>
                            <mat-error class="text-xs">กรุณาเลือกสมาชิกอย่างน้อย 1 สมาชิก</mat-error>
                        </mat-form-field>
                    </div>
                    <div class="md:w-full" *ngIf="item === 'branch-code-multi'">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>สาขา</mat-label>
                            <mat-select [formControlName]="'branchCodes'" multiple (selectionChange)="selectionBranchChanged($event.value)">
                                <div class="px-3 pb-1 w-full">
                                    <input class="px-3 py-2 border-[1px] w-full rounded-[8px]" (input)="searchBranchChange($event)" placeholder="Search"/>
                                </div>
                                <mat-option [disabled]="true" value="">เลือกสาขา</mat-option>
                                <mat-option value="all">เลือกสาขาทั้งหมด</mat-option>
                                <mat-option *ngFor="let branch of branchData" [value]="branch.code">
                                    {{branch.name ?? ''}}
                                </mat-option>
                            </mat-select>
                            <mat-error class="text-xs">กรุณาเลือกสาขาอย่างน้อย 1 สาขา</mat-error>
                        </mat-form-field>
                    </div>
                    <div class="md:w-full" *ngIf="item === 'payment-method'">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>ประเภทการจ่าย</mat-label>
                            <mat-select [formControlName]="'paymentMethods'" multiple (selectionChange)="selectionPaymentChanged($event.value)">
                                <mat-option [disabled]="true" value="">เลือกประเภทการจ่าย</mat-option>
                                <mat-option *ngFor="let payment of paymentData" [value]="payment.id">
                                    {{payment.name ?? ''}}
                                </mat-option>
                            </mat-select>
                            <mat-error class="text-xs">กรุณาเลือกประเภทการจ่ายอย่างน้อย 1 ประเภท</mat-error>
                        </mat-form-field>
                    </div>
                    <div class="md:w-full" *ngIf="item === 'toggle-buffet'">
                        <mat-slide-toggle [formControlName]="'includeBuffet'" [color]="'primary'">รวมบุฟเฟ่ต์</mat-slide-toggle>
                    </div>
                </ng-container>
            </div>
        </form>
    </div>
    <!-- <div> check : {{this.form.value | json}}</div> -->
    <div mat-dialog-actions class="flex justify-end">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
            ตกลง
        </button>
        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
            ยกเลิก
        </button>
    </div>
</div>
