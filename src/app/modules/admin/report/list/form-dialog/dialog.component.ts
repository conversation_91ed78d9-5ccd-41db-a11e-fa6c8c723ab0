import { Subscription } from 'rxjs';
import { Component, OnInit, OnChanges, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataTablesModule } from 'angular-datatables';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import {
    MatDialog,
    MatDialogTitle,
    MatDialogContent,
    MatDialogActions,
    MatDialogClose,
    MatDialogRef,
    MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { FormBuilder, FormControl, FormGroup, FormsModule, Validators } from '@angular/forms';
import { MatToolbarModule } from '@angular/material/toolbar';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { ReportListService } from '../report-list.service';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { ToastrService } from 'ngx-toastr';
import { MatRadioModule } from '@angular/material/radio';
import { DateTime } from 'luxon';
import { createFileFromBlob } from 'app/modules/shared/helper';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
@Component({
    selector: 'app-device-form',
    standalone: true,
    templateUrl: './dialog.component.html',
    styleUrl: './dialog.component.scss',
    imports: [CommonModule, DataTablesModule, MatIconModule, MatFormFieldModule, MatInputModule,
        FormsModule, MatToolbarModule,
        MatButtonModule,
        MatDialogTitle,
        MatDialogContent,
        MatDialogActions,
        MatDialogClose,
        MatSelectModule,
        ReactiveFormsModule,
        MatRadioModule,
        MatDatepickerModule,
        MatSlideToggleModule
    ]
})
export class DialogForm implements OnInit {

    form: FormGroup;
    stores: any[] = [];
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    dtOptions: DataTables.Settings = {};
    addForm: FormGroup;
    walletTypeData: any[] = [];
    cardTypeData: any[] = [];
    foodTypeData: any[] = [];

    deviceData: any
    alldeviceData: any
    check_alldevice: boolean = false

    memberData: any
    allmemberData: any
    check_allmember: boolean = false

    branchData: any
    allbranchData: any
    check_allbranch: boolean = false

    searchDevice: any
    paymentData: any =[
        {id: '1', name: 'เงินสด', type: 'cash'},
        {id: '2', name: 'พร้อมเพย์', type: 'thaiqr'},
        {id: '3', name: 'แม่มณี', type: 'cash'},
        {id: '4', name: 'สมาชิก', type: 'member'},
    ]
    constructor(
        private dialogRef: MatDialogRef<DialogForm>,
        @Inject(MAT_DIALOG_DATA) public data: any,
        public dialog: MatDialog,
        private FormBuilder: FormBuilder,
        public _service: ReportListService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
    ) {
        this._service.getDevice().subscribe({
            next:(resp)=> {
                this.alldeviceData = resp
                this.deviceData = resp
                //console.log('this.deviceData',this.deviceData);
                if (this.deviceData.length == 0){
                    this.deviceData = [{id: 'not found', name: 'ไม่พบอุปกรณ์'}]
                }
            }
        })
        this._service.getMember().subscribe({
            next:(resp)=> {
                this.allmemberData = resp
                this.memberData = resp
                //console.log('this.memberData',this.memberData);
                if (this.memberData.length == 0){
                    this.memberData = [{id: 'not found', name: 'ไม่พบสมาชิก'}]
                }
            }
        })
        this._service.getBranch().subscribe({
            next:(resp)=> {
                this.allbranchData = resp
                this.branchData = resp
                console.log('this.branchData',this.branchData);
                if (this.branchData.length == 0){
                    this.branchData = [{id: 'not found', name: 'ไม่พบสาขา'}]
                }
            }
        })
        this.form = this.FormBuilder.group({
            date: '',
            startDate: '',
            endDate: '',
            categoryId: '',
            branchId: '',
            walletType: '',
            cardType: '',
            foodType: '',
            deviceId: '',
            deviceIds: [['']],
            memberIds: [['']],
            branchCodes: [['']],
            paymentMethods: [['']],
            includeBuffet: false,
        });        
    }

    ngOnInit(): void {
        this.walletTypeData =  [
            {
                key: 'WAL',
                name: 'EL1 Personal Wallet'
            },
            {
                key: 'EL2',
                name: 'EL2 OT Credit'
            },
            {
                key: 'EL4',
                name: 'EL4 VIP Credit'
            },
        ];
        this.cardTypeData = [
            'A', 'B'
        ]
        this.foodTypeData = [
            'Western',
            'Asian',
            'Noodle'
        ]
    }

    selectionDeviceChanged(data: any){
        let temp_deviceIds = data.filter(item => item !== '');
        const findAll = temp_deviceIds.find(item => item === 'all');
        
        if (this.check_alldevice == true) {
            this.check_alldevice = false //ไม่เลือกทั้งหมด
            //let device_all = this.alldeviceData.map(item => item.id)
            //const new_selectedDevice = device_all.filter(item => !temp_deviceIds.includes(item) && item != 'all');
            if (findAll === undefined){
                this.form.patchValue({
                    deviceIds: ['']
                });
            } else {
                const new_selectedDevice = temp_deviceIds.filter(item => item != 'all');
                this.form.patchValue({
                    deviceIds: new_selectedDevice
                });
            }
        } else if (findAll !== undefined && this.check_alldevice == false) { // all ครั้งแรก
            this.check_alldevice = true
            
            let device_all = this.alldeviceData.map(item => item.id)
            device_all.push('all')
            this.form.patchValue({
                deviceIds: device_all
            });
        } else{
            this.form.patchValue({
                deviceIds: temp_deviceIds
            });
        }
        if (this.form.value.deviceIds.length == 0){
            this.form.patchValue({
                deviceIds : ['']
            })
        }
        console.log('Selected Device IDs:', this.form.get('deviceIds')?.value);
    }

    selectionMemberChanged(data: any){
        let temp_memberIds = data.filter(item => item !== '');
        const findAll = temp_memberIds.find(item => item === 'all');

        if (this.check_allmember == true) {
            this.check_allmember = false //ไม่เลือกทั้งหมด
            if(findAll === undefined){
                this.form.patchValue({
                    memberIds: ['']
                });
            } else {
                //let member_all = this.allmemberData.map(item => item.id)
                const new_selectedMember = temp_memberIds.filter(item => item != 'all');
                this.form.patchValue({
                    memberIds: new_selectedMember
                });
            }
        } else if (findAll !== undefined && this.check_allmember == false) { // all ครั้งแรก
            this.check_allmember = true
            
            let member_all = this.allmemberData.map(item => item.id)
            member_all.push('all')
            this.form.patchValue({
                memberIds: member_all
            });
        } else{
            this.form.patchValue({
                memberIds: temp_memberIds
            });
        }

        if (this.form.value.memberIds.length == 0){
            this.form.patchValue({
                memberIds : ['']
            })
        }
        
        console.log('Selected Member IDs:', this.form.get('memberIds')?.value);
    }

    selectionBranchChanged(data: any){
        let temp_branchCodes = data.filter(item => item !== '');
        const findAll = temp_branchCodes.find(item => item === 'all');

        if (this.check_allbranch == true) {
            this.check_allbranch = false //ไม่เลือกทั้งหมด
            //let branch_all = this.allbranchData.map(item => item.code)
            //const new_selectedBranch = branch_all.filter(item => !temp_branchCodes.includes(item) && item != 'all');
            if (findAll === undefined){
                this.form.patchValue({
                    branchCodes: ['']
                });
            } else {
                const new_selectedBranch = temp_branchCodes.filter(item => item != 'all');
                this.form.patchValue({
                    branchCodes: new_selectedBranch
                });
            }
        } else if (findAll !== undefined && this.check_allbranch == false) { // all ครั้งแรก
            this.check_allbranch = true
            
            let branch_all = this.allbranchData.map(item => item.code)
            branch_all.push('all')
            this.form.patchValue({
                branchCodes: branch_all
            });
        } else{
            this.form.patchValue({
                branchCodes: temp_branchCodes
            });
        }

        if (this.form.value.memberIds.length == 0){
            this.form.patchValue({
                branchCodes : ['']
            })
        }
        
        console.log('Selected Branch Codes:', this.form.get('branchCodes')?.value);
    }

    selectionPaymentChanged(data: any){
        let temp_Payment = data.filter(item => item !== '');

        if (temp_Payment.length == 0){
            this.form.patchValue({
                paymentMethods : ['']
            })
        } else {
            this.form.patchValue({
                paymentMethods : temp_Payment
            })
        }
        
        console.log('Selected Payment Methods:', this.form.get('paymentMethods')?.value);
    }

    searchDeviceChange(data: Event){
        const inputValue = (data.target as HTMLInputElement).value;
        console.log('Input value changed:', inputValue);
         
        if (inputValue != ''){
            this.deviceData = this.alldeviceData.filter(item => item.name.toLowerCase().includes(inputValue.toLowerCase()))
            console.log('this.deviceData kub',inputValue,' :: ',this.deviceData);
        }else{
            this.deviceData = this.alldeviceData
        }
    }

    searchMemberChange(data: Event){
        const inputValue = (data.target as HTMLInputElement).value;
        console.log('Input value changed:', inputValue);
         
        if (inputValue != ''){
            this.memberData = this.allmemberData.filter(item => item.firstname?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                item.middlename?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                item.lastname?.toLowerCase().includes(inputValue.toLowerCase()))
            console.log('this.deviceData kub',inputValue,' :: ',this.memberData);
        }else{
            this.memberData = this.allmemberData
        }
    }
    
    searchBranchChange(data: Event){
        const inputValue = (data.target as HTMLInputElement).value;
        console.log('Input value changed:', inputValue);
         
        if (inputValue != ''){
            this.branchData = this.allbranchData.filter(item => item.firstname?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                item.middlename?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                item.lastname?.toLowerCase().includes(inputValue.toLowerCase()))
            console.log('this.branchData kub',inputValue,' :: ',this.branchData);
        }else{
            this.branchData = this.allbranchData
        }
    }

    Submit() {
        if (this.data.value.code === 'reportOrderBranch') {
            this.reportOrderBranch()
        } else if (this.data.value.code === 'reportOrderBranchCategory') {
            this.reportOrderBranchCategory()
        } else if (this.data.value.code === 'reportOrderBranchProduct') {
            this.reportOrderBranchProduct()
        } else if (this.data.value.code === 'reportOrderType') {
            this.reportOrderType()
        } else if (this.data.value.code === 'reportCreditPerdate') {
            this.reportCreditPerdate()
        } else if (this.data.value.code === 'reportTaplogSummary') {
            this.reportTaplogSummary()
        } else if (this.data.value.code === 'reportTopUpHistory') {
            this.reportTopUpHistory()
        } else if (this.data.value.code === 'reportEmployeeTapCardType') {
            this.reportEmployeeTapCardType()
        } else if (this.data.value.code === 'reportPaymentTopup') {
            this.reportPaymentTopup()
        } else if (this.data.value.code === 'reportCardMovement') {
            this.reportCardMovement()
        } else if (this.data.value.code === 'reportRemainCreditDaily') {
            this.reportRemainCreditDaily()
        } else if (this.data.value.code === 'reportBuffet') {
            this.reportBuffet()
        } else if (this.data.value.code === 'reportDeviceHistory') {
            this.reportDeviceHistory()
        } else if (this.data.value.code === 'reportHistoryReserve') {
            this.reportHistoryReserve()
        } else if (this.data.value.code === 'reportCardPayment') {
            this.reportCardPayment()
        } else if (this.data.value.code === 'reportItemSaleTransaction') {
            this.reportItemSaleTransaction()
        } else if (this.data.value.code === 'reportHistoryOrderMenu') {
            this.reportHistoryOrderMenu()
        } else if (this.data.value.code === 'reportTop10') {
            this.reportTop10()
        } else if (this.data.value.code === 'reportSaleFilterByProduct') {
            this.reportSaleFilterByProduct()
        } else if (this.data.value.code === 'reportCreditChange') {
            this.reportCreditChange()
        } else if (this.data.value.code === 'reportShopHistory') {
            this.reportShopHistory()
        }
        
        // else if (this.data.value.code === 'tapLogSummaryShift') {
        //    this.tapLogSummaryShift()
        //} else if (this.data.value.code === 'tapLogSummaryMember') {
        //    this.tapLogSummaryMember()
        //} else if (this.data.value.code === 'tapLogReportToday') {
        //    this.tapLogReportToday()
        //} else if (this.data.value.code === 'paymentMethodHistory') {
        //    this.paymentMethodHistory()
        //} else if (this.data.value.code === 'summaryPaidCard') {
        //    this.summaryPaidCard()
        //} else if (this.data.value.code === 'cashierOutlet') {
        //    this.cashierOutlet()
        //}
        return;
    }

    onClose() {
        this.dialogRef.close()
    }


    reportOrderBranch() {
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
       
        let branchId = localStorage.getItem('branch')
        this._service.reportOrderBranch({ startDate: startDate, endDate: endDate, branchId: branchId }).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `summary_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_OrderBranch_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }
    reportOrderBranchCategory() {
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
       
        let branchId = localStorage.getItem('branch')
        this._service.reportOrderBranchCategory({ startDate: startDate, endDate: endDate, branchId: branchId }).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `summary_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_OrderBranchCategory_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }
    reportOrderBranchProduct() {
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
       
        let branchId = localStorage.getItem('branch')
        this._service.reportOrderBranchProduct({ startDate: startDate, endDate: endDate, branchId: branchId }).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `summary_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_OrderBranchProduct_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }
    reportOrderType() {
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
       
        let branchId = localStorage.getItem('branch')
        this._service.reportOrderType({ startDate: startDate, endDate: endDate, branchId: branchId }).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `summary_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_OrderType_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }
    reportCreditPerdate() {
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
       
        let branchId = localStorage.getItem('branch')
        this._service.reportCreditPerdate({ startDate: startDate, endDate: endDate, branchId: branchId }).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `summary_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_CreditPerdate_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }
    reportTaplogSummary() {
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
       
        let branchId = localStorage.getItem('branch')
        this._service.reportTaplogSummary({ startDate: startDate, endDate: endDate, branchId: branchId }).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `summary_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_TaplogSummary_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }
    reportTopUpHistory() {
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
       
        let branchId = localStorage.getItem('branch')
        this._service.reportTopUpHistory({ startDate: startDate, endDate: endDate, branchId: branchId }).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `summary_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_TopUpHistory_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }

    reportRemainCreditDaily() { // 1
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
        let branchId = localStorage.getItem('branch')
       
        this._service.reportRemainCreditDaily({ startDate: startDate, endDate: endDate, branchId: branchId }).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `summary_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_RemainCreditDaialy_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }
    
    reportPaymentTopup() { //2
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
       
        //, walType: formValue.walletType
        this._service.reportPaymentTopup({ startDate: startDate, endDate: endDate }).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `summary_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_PaymentTopup_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }

    reportCardMovement() { //use 10
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
        //, memberId: 0 
        this._service.reportCardMovement({ startDate: startDate, endDate: endDate}).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `POS_ReportCardMovement_Sum_Essilor_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_CardMovement_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }

    reportBuffet() { //use 3
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }

        this._service.reportBuffet({ startDate: startDate, endDate: endDate, foodType: formValue.foodType }).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `POS_TapLogCardDetail_Sum_Essilor_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_Buffet_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }

    reportEmployeeTapCardType() { //use 3
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
        let branchId = localStorage.getItem('branch')
        this._service.reportEmployeeTapCardType({ startDate: startDate, endDate: endDate, Type: formValue.cardType, branchId: branchId }).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `POS_TapLogCardDetail_Sum_Essilor_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_EmployeeTapCardType_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }

    reportDeviceHistory(){
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }

        this._service.reportDeviceHistory({ startDate: startDate, endDate: endDate, deviceId: formValue.deviceId }).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `POS_TapLogCardDetail_Sum_Essilor_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_DeviceHistory_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }

    reportHistoryReserve(){
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }

        this._service.reportHistoryReserve({ startDate: startDate, endDate: endDate }).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `POS_TapLogCardDetail_Sum_Essilor_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_HistoryReserve_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }

    reportCardPayment(){
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
        console.log(formValue.memberIds);
        
        //formValue.memberIds = formValue.memberIds.filter(item => item !== '' && item !== 'all' )
        //, memberIds: formValue.memberIds
        this._service.reportCardPayment({ startDate: startDate, endDate: endDate }).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `POS_TapLogCardDetail_Sum_Essilor_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_CardPayment_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }

    reportItemSaleTransaction(){
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
        formValue.deviceIds = formValue.deviceIds.filter(item => item !== '' && item !== 'all' )
        formValue.paymentMethods = formValue.paymentMethods.filter(item => item !== '' && item !== 'all' )

        this._service.reportItemSaleTransaction({ startDate: startDate, endDate: endDate, deviceIds: formValue.deviceIds, paymentMethods: formValue.paymentMethods, includeBuffet: formValue.includeBuffet }).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `POS_TapLogCardDetail_Sum_Essilor_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_ItemSaleTransaction_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }

    reportHistoryOrderMenu(){
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
        formValue.memberIds = formValue.memberIds.filter(item => item !== '' && item !== 'all' )

        this._service.reportHistoryOrderMenu({ startDate: startDate, endDate: endDate, memberIds: formValue.memberIds}).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `POS_TapLogCardDetail_Sum_Essilor_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_HistoryOrderMenu_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }

    reportTop10(){
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
        formValue.branchCodes = formValue.branchCodes.filter(item => item !== '' && item !== 'all' )

        this._service.reportTop10({ startDate: startDate, endDate: endDate, branchCodes: formValue.branchCodes}).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `POS_TapLogCardDetail_Sum_Essilor_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_Top10SaleOrderSummary_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }

    reportSaleFilterByProduct(){
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
        formValue.branchCodes = formValue.branchCodes.filter(item => item !== '' && item !== 'all' )
        formValue.deviceIds = formValue.deviceIds.filter(item => item !== '' && item !== 'all' )
//branchCodes: formValue.branchCodes, 
        this._service.reportSaleFilterByProduct({ startDate: startDate, endDate: endDate, deviceIds: formValue.deviceIds}).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `POS_TapLogCardDetail_Sum_Essilor_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_SalesFilterByBranchDevice_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }

    reportCreditChange(){
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
        
        formValue.memberIds = formValue.memberIds.filter(item => item !== '' && item !== 'all' )
        //, memberIds: formValue.memberIds
        this._service.reportChangeCredit({ startDate: startDate, endDate: endDate , memberIds: formValue.memberIds}).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `POS_TapLogCardDetail_Sum_Essilor_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_CardPayment_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }
    reportShopHistory(){
        let formValue = this.form.value
        if (formValue.startDate && formValue.startDate) {
            var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
            var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
        }

        if (!startDate || !endDate) {
            this.toastr.error('กรุณาเลือกวันที่')
            return;
        }
        formValue.deviceIds = formValue.deviceIds.filter(item => item !== '' && item !== 'all' )

        this._service.reportShopHistory({ startDate: startDate, endDate: endDate, deviceId: formValue.deviceIds }).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `POS_TapLogCardDetail_Sum_Essilor_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_DeviceHistory_${startDate}_${endDate}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }
    //tapLogSummaryShift() {
    //    let formValue = this.form.value
    //    if (formValue.startDate && formValue.startDate) {
    //        var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
    //        var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
    //    }

    //    if (!startDate || !endDate) {
    //        this.toastr.error('กรุณาเลือกวันที่')
    //        return;
    //    }

    //    if (!formValue.cardType) {
    //        this.toastr.error('กรุณาเลือกประเภทบัตร')
    //        return;
    //    }

    //    this._service.tapSummaryShift({ startDate: startDate, endDate: endDate, cardType: formValue.cardType }).subscribe({
    //        next: (resp) => {
    //            this.toastr.success('ดำเนินการสำเร็จ')
    //            createFileFromBlob(resp, `summary_${startDate}_${endDate}.xlsx`);
    //        },
    //        error: (err) => {
    //            this.toastr.error('เกิดข้อผิดพลาด')
    //        }
    //    })
    //}
    //tapLogSummaryMember() {
    //    let formValue = this.form.value
    //    if (formValue.startDate && formValue.startDate) {
    //        var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
    //        var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
    //    }

    //    if (!startDate || !endDate) {
    //        this.toastr.error('กรุณาเลือกวันที่')
    //        return;
    //    }

    //    if (!formValue.cardType) {
    //        this.toastr.error('กรุณาเลือกประเภทบัตร')
    //        return;
    //    }

    //    this._service.tapSummaryMember({ startDate: startDate, endDate: endDate, cardType: formValue.cardType, memberId: 0 }).subscribe({
    //        next: (resp) => {
    //            this.toastr.success('ดำเนินการสำเร็จ')
    //            createFileFromBlob(resp, `summary_${startDate}_${endDate}.xlsx`);
    //        },
    //        error: (err) => {
    //            this.toastr.error('เกิดข้อผิดพลาด')
    //        }
    //    })
    //}
    //tapLogReportToday() {
    //    let formValue = this.form.value
    //    var date = DateTime.fromISO(formValue.date).toFormat('yyyy-MM-dd');
    //    if (!formValue.date) {
    //        this.toastr.error('กรุณาเลือกวันที่')
    //        return;
    //    }

    //    this._service.tapLogToday({ date: date}).subscribe({
    //        next: (resp) => {
    //            this.toastr.success('ดำเนินการสำเร็จ')
    //            createFileFromBlob(resp, `report_tap_log_today${date}.xlsx`);
    //        },
    //        error: (err) => {
    //            this.toastr.error('เกิดข้อผิดพลาด')
    //        }
    //    })
    //}
    //paymentMethodHistory() {
    //    let formValue = this.form.value
    //    if (formValue.startDate && formValue.startDate) {
    //        var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
    //        var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
    //    }

    //    if (!startDate || !endDate) {
    //        this.toastr.error('กรุณาเลือกวันที่')
    //        return;
    //    }

    //    this._service.paymentMethodHistory({ startDate: startDate, endDate: endDate }).subscribe({
    //        next: (resp) => {
    //            this.toastr.success('ดำเนินการสำเร็จ')
    //            createFileFromBlob(resp, `POS_LogCardPayment_Sum_${startDate}_${endDate}.xlsx`);
    //        },
    //        error: (err) => {
    //            this.toastr.error('เกิดข้อผิดพลาด')
    //        }
    //    })
    //}
    //summaryPaidCard() {
    //    let formValue = this.form.value
    //    if (formValue.startDate && formValue.startDate) {
    //        var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
    //        var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
    //    }

    //    if (!startDate || !endDate) {
    //        this.toastr.error('กรุณาเลือกวันที่')
    //        return;
    //    }


    //    if (!formValue.walletType) {
    //        this.toastr.error('กรุณาเลือกประเภทกระเป๋า')
    //        return;
    //    }

    //    this._service.summaryPaidCard({ startDate: startDate, endDate: endDate, walleType: formValue.walletType }).subscribe({
    //        next: (resp) => {
    //            this.toastr.success('ดำเนินการสำเร็จ')
    //            createFileFromBlob(resp, `POS_RepTopup_Balance_Site${startDate}_${endDate}.xlsx`);
    //        },
    //        error: (err) => {
    //            this.toastr.error('เกิดข้อผิดพลาด')
    //        }
    //    })
    //}
    //cashierOutlet() {
    //    let formValue = this.form.value
    //    if (formValue.startDate && formValue.startDate) {
    //        var startDate = DateTime.fromISO(formValue.startDate).toFormat('yyyy-MM-dd');
    //        var endDate = DateTime.fromISO(formValue.endDate).toFormat('yyyy-MM-dd');
    //    }

    //    if (!startDate || !endDate) {
    //        this.toastr.error('กรุณาเลือกวันที่')
    //        return;
    //    }

    //    this._service.cashierOutlet({ startDate: startDate, endDate: endDate }).subscribe({
    //        next: (resp) => {
    //            this.toastr.success('ดำเนินการสำเร็จ')
    //            createFileFromBlob(resp, `POS_RepCashierOutlet_Sum_Essilor_${startDate}_${endDate}.xlsx`);
    //        },
    //        error: (err) => {
    //            this.toastr.error('เกิดข้อผิดพลาด')
    //        }
    //    })
    //}
    
}
