<div class="flex flex-col flex-auto min-w-0">
    <div class="flex-auto p-6 sm:p-10 bg-card m-4 rounded-md">
        <div class="flex flex-row items-center justify-between p-3 bg-card dark:bg-transparent">
            <div class="flex flex-row gap-4 items-center justify-between">
                <h2 class="text-2xl md:text-2xl tracking-tight leading-7 sm:leading-10 truncate">
                    รายงานยอดขายแยกตามพนักงาน
                </h2>
            </div>
            <form action="" [formGroup]="form">
                <div class="flex flex-row gap-4 min-w-56">
                    <mat-form-field class="w-full">
                        <mat-select formControlName="payment_type">
                            <mat-option [value]="''">
                                เลือกพนักงาน
                            </mat-option>
                            <mat-option *ngFor="let item of users;" [value]="item.id">
                                {{item.name}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
            </form>
        </div>
        <table datatable [dtOptions]="dtOptions" class="row-border hover w-full"></table>
    </div>
</div>