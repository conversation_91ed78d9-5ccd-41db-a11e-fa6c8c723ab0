import { CommonModule, DatePipe, registerLocaleData } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, LOCALE_ID, OnInit, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { BookDailyMealService } from './book-daily-meal.service';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { FilePickerModule } from 'ngx-awesome-uploader';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { DialogRef } from '@angular/cdk/dialog';
import { DialogForm } from './form-dialog/dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { MatDatepickerInputEvent, MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { ReactiveFormsModule, FormsModule, FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { DateTime } from 'luxon';
import { createFileFromBlob } from 'app/modules/shared/helper';
import localeTH from '@angular/common/locales/th';

@Component({
    selector: 'app-book-daily-meal',
    standalone: true,
    providers: [
        DatePipe
    ],
    imports: [
        CommonModule,
        DataTablesModule,
        MatButtonModule,
        MatIconModule,
        FilePickerModule,
        MatMenuModule,
        MatDividerModule,
        MatDatepickerModule,
        MatFormFieldModule,
        FormsModule,
        ReactiveFormsModule,
        MatInputModule,
    ],
    templateUrl: './book-daily-meal.component.html',
    styleUrl: './book-daily-meal.component.scss',
    changeDetection: ChangeDetectionStrategy.Default,
})
export class BookDailyMealComponent implements OnInit, AfterViewInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();

    @ViewChild('btNg') btNg: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;


    datePicked: Date | null = null;
    data_day: string | number;
    data_month: string | number;
    data_year: string | number;
    constructor(
        private bookDailyMealService: BookDailyMealService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        public dialog: MatDialog,
        private datePipe: DatePipe
    ) {
        registerLocaleData(localeTH); // ลงทะเบียนข้อมูล locale ภาษาไทย
        this.datePicked = new Date()
        this.filterdateFirst()
    }
    ngOnInit(): void {
        setTimeout(() =>
            this.loadTable()
        );
    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }

    getFormattedDate(): string | null {
        return this.datePicked ? this.datePipe.transform(this.datePicked, 'yyyy-MM-dd') : null;
    }

    formatDate(date: Date): string {
        if (!date) return '';
        const day = date.getDate();
        const month = date.toLocaleString('en-US', { month: 'long' }); // เลือกเป็น long เพื่อให้เป็นชื่อเดือนเต็ม เช่น January
        const year = date.getFullYear();
        return `${day} ${month} ${year}`;
    }

    filterdate(){
        console.log("datePicked: ",this.datePicked);
        this.data_day = this.datePicked ? this.datePipe.transform(this.datePicked, 'dd') : 0;
        this.data_month = this.datePicked ? this.datePipe.transform(this.datePicked, 'MM') : 12;
        this.data_year = this.datePicked ? this.datePipe.transform(this.datePicked, 'yyyy') : 2024;
        console.log(`${this.data_day}, ${this.data_month}, ${this.data_year}`);
        this.rerender()
    }

    filterdateFirst(){
        console.log("datePicked: ",this.datePicked);
        this.data_day = this.datePicked ? this.datePipe.transform(this.datePicked, 'dd') : 0;
        this.data_month = this.datePicked ? this.datePipe.transform(this.datePicked, 'MM') : 12;
        this.data_year = this.datePicked ? this.datePipe.transform(this.datePicked, 'yyyy') : 2024;
        console.log(`${this.data_day}, ${this.data_month}, ${this.data_year}`);
    }

    loadTable(): void {
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,     // Set the flag
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.filter = {
                        'filter.orderDate': (this.data_day && this.data_month && this.data_year) ? `$btw:${this.data_year}-${this.data_month}-${this.data_day} 00:00:00,${this.data_year}-${this.data_month}-${this.data_day} 23:59:59` : '' ,
                        'filter.orderType': 'reserve',
                }
                this.bookDailyMealService.datatable(dataTablesParameters).subscribe({
                    next: (resp: any) => {
                        console.log('resp',resp);
                        
                        callback({
                            recordsTotal: resp?.meta?.totalItems,
                            recordsFiltered: resp?.meta?.totalItems,
                            data: resp?.data
                        });
                    }, error(err) {
                        console.log(err);
                        console.log('error datatable bookmeal');
                        this.toastr.error('เกิดข้อผิดพลาด')
                    },
                })
            },
            columns: [
                {
                    title: 'ลำดับ',
                    data: 'no',
                    defaultContent: '',
                    className: 'w-15 text-center'
                },
                {
                    title: 'วันที่',
                    data: function(row: any) {
                        if (row.orderDate)
                            return DateTime.fromISO(row.orderDate).toFormat('dd/MM/yyyy');
                        else
                            return '-'
                    },
                    defaultContent: '',
                    className: 'text-center'
                },
                {
                    title: 'รหัส',
                    data: 'member.code',
                    defaultContent: '',
                    className: 'text-center'
                },
                {
                    title: 'ชื่อ',
                    data: 'member.firstname',
                    defaultContent: '',
                    className: 'text-center'
                },
                {
                    title: 'นามสุกล',
                    data: 'member.lastname',
                    defaultContent: '',
                    className: 'text-center'
                },
                {
                    title: 'อาหาร',
                    data: 'orderItems[0].product.name',
                    defaultContent: '',
                    className: 'text-center'
                },
                {
                    title: 'สถานะ',
                    data: 'orderStatus',
                    defaultContent: '',
                    className: 'text-center'
                },
                {
                     title: 'จัดการ',
                     data: null,
                     defaultContent: '',
                     ngTemplateRef: {
                         ref: this.btNg,
                     },
                     className: 'w-15 text-center'
                }
            ]
        }
    }

    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(this.dtOptions);
        });
    }

    opendialogapro() {
        const DialogRef = this.dialog.open(DialogForm, {
            disableClose: true,
            width: '500px',
            height: 'auto',
            enterAnimationDuration: 300,
            exitAnimationDuration: 300,
            data: {
                type: 'NEW'
            }
        });
        DialogRef.afterClosed().subscribe((result) => {
            if (result) {
                console.log(result, 'result')
                this.rerender();
            }
        });
    }

    openDialogEdit(item: any) {
        const DialogRef = this.dialog.open(DialogForm, {
            disableClose: true,
            width: '500px',
            height: 'auto',
            enterAnimationDuration: 300,
            exitAnimationDuration: 300,
            data: {
                type: 'EDIT',
                value: item
            }
        });
        DialogRef.afterClosed().subscribe((result) => {
            if (result) {
                console.log(result, 'result')
                this.rerender();
            }
        });
    }

    clickDelete(id: any) {
        const confirmation = this.fuseConfirmationService.open({
            title: "ยืนยันลบข้อมูล",
            message: "กรุณาตรวจสอบข้อมูล หากลบข้อมูลแล้วจะไม่สามารถนำกลับมาได้",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "warn"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "ยืนยัน",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "ยกเลิก"
                }
            },
            dismissible: false
        })
        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    this.bookDailyMealService.delete(id).subscribe({
                        error: (err) => {

                        },
                        complete: () => {
                            this.toastr.success('ดำเนินการลบสำเร็จ');
                            this.rerender();
                        },
                    });
                }
            }
        )
    }

    downloadExcel(){
        this.bookDailyMealService.reportHistoryReserve({ startDate: `${this.data_year}-${this.data_month}-${this.data_day}`, endDate: `${this.data_year}-${this.data_month}-${this.data_day}` }).subscribe({
            next: (resp) => {
                this.toastr.success('ดำเนินการสำเร็จ')
                //createFileFromBlob(resp, `summary_${startDate}_${endDate}.xlsx`);
                createFileFromBlob(resp, `Report_HistoryReserve_${this.data_year}-${this.data_month}-${this.data_day}.xlsx`);
            },
            error: (err) => {
                this.toastr.error('เกิดข้อผิดพลาด')
            }
        })
    }

    void_order(id: any){
        const confirmation = this.fuseConfirmationService.open({
            title: "คุณต้องการ void ใช่หรือไม่ ?",
            message: "กรุณาตรวจสอบให้แน่ใจ หากยืนยันแล้วจะไม่สามารถย้อนกลับ",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "warn"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "ยืนยัน",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "ยกเลิก"
                }
            },
            dismissible: false
        })
        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    this.bookDailyMealService.void_order(id).subscribe({
                        complete:()=>{
                            this.toastr.success('ดำเนินการ void สำเร็จ')
                            this.rerender()
                        },
                        error:()=>{
                            this.toastr.error('เกิดข้อผิดพลาด')
                        }
                    })
                }
            }
        )
    }
}
