<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
    <div class="flex flex-row justify-between pb-2 mt-5">
      <div>
        <h2 class="text-2xl md:text-xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
          รายการจองอาหารประจำวัน
        </h2>
      </div>
      <div class="flex">
        <button mat-flat-button class="bg-[rgb(2,124,57)] mr-4" (click)="downloadExcel()" ><!--class="translate-y-[10%]"-->
          <mat-icon class="icon-size-5" svgIcon="heroicons_solid:table-cells"></mat-icon>
          <span class="ml-2 text-white"> Excel</span>
        </button>
        <button mat-flat-button [color]="'primary'" (click)="opendialogapro()" ><!--class="translate-y-[10%]"-->
          <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:plus-circle'"></mat-icon>
          <span class="ml-2"> นำเข้าข้อมูล</span>
        </button>
      </div>
    </div>
    <div class="flex items-center justify-start">
      <mat-form-field appearance="fill">
        <input matInput [matDatepicker]="picker" [(ngModel)]="datePicked" placeholder="วันที่" (dateChange)="filterdate()">
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
      </mat-form-field>
    </div>
    <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="row-border hover w-full">
    </table>
  </div>
</div>

<ng-template #btNg let-data="adtData">
  <button mat-icon-button [color]="'primary'" [matMenuTriggerFor]="menu">
    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cog-8-tooth'"></mat-icon>
  </button>
  <mat-menu #menu="matMenu">
    <!--<button mat-menu-item (click)="openDialogEdit(data)">
      <mat-icon [svgIcon]="'heroicons_solid:pencil-square'"></mat-icon>
      <span>แก้ไข</span>
    </button>
    <mat-divider></mat-divider>-->
    <button mat-menu-item (click)="void_order(data.id)">
      <mat-icon [svgIcon]="'heroicons_outline:receipt-refund'"></mat-icon>
      <span>void</span>
    </button>
    <!--<button mat-menu-item (click)="clickDelete(data.id)">
      <mat-icon [svgIcon]="'heroicons_solid:trash'"></mat-icon>
      <span>ลบ</span>
    </button>-->
  </mat-menu>
</ng-template>
