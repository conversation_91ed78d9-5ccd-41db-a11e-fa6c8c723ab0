<div class="md:max-w-2xl">
    <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'NEW'">เพิ่มข้อมูล</h1>
    <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'EDIT'">แก้ไขข้อมูล</h1>
    <div mat-dialog-content class="overflow-y-auto md:max-h-180">
        <form [formGroup]="form">
            <div class="flex-auto">
                <div class="flex flex-col md:flex mb-6">
                    <div class="md:w-full flex flex-col md:flex-row gap-1 mb-1">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-1/3" appearance="fill">
                            <mat-label>รหัสโปรโมชั่น</mat-label>
                            <input matInput [placeholder]="'กรุณาระบุรหัสโปรโมชั่น'" formControlName="code"
                                [readonly]="this.data.type === 'EDIT'">
                        </mat-form-field>
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-2/3">
                            <mat-label>ชื่อโปรโมชั่น</mat-label>
                            <input matInput [placeholder]="'กรุณาระบุชื่อโปรโมชั่น'" formControlName="name"
                                [readonly]="this.data.type === 'EDIT'">
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>รายละเอียด</mat-label>
                            <input matInput [placeholder]="'กรุณาระบุรายละเอียด'" formControlName="detail">
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <div class="md:w-full flex flex-col md:flex-row gap-1">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-1/3">
                                <mat-label>ประเภทการลด</mat-label>
                                <mat-select (selectionChange)="onDisplayChange($event.value)" [placeholder]="'ระบุประเภทการลด'">
                                    <mat-option *ngFor="let item of displays; let i = index;"
                                        [value]="item.id">{{item.name}}</mat-option>
                                </mat-select>
                            </mat-form-field>
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-1/3" *ngIf="this.display === 'percent'">
                                <mat-label>ราคา</mat-label>
                                <input matInput [formControlName]="'amount'" [min]="0" [max]="100" required 
                                    [placeholder]="'ระบุเปอร์เซ็น'">
                            </mat-form-field>
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-1/3"  *ngIf="this.display === 'price'">
                                <mat-label>ราคา</mat-label>
                                <input matInput mask="separator.2" thousandSeparator="," separatorLimit="99999999"
                                    [allowNegativeNumbers]="false" [formControlName]="'amount'" required
                                    [placeholder]="'ระบุราคา'">
                            </mat-form-field>
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-1/3">
                                <mat-label>แต้ม</mat-label>
                                <input matInput mask="separator.2" thousandSeparator="," separatorLimit="99999999"
                                    [allowNegativeNumbers]="false" [formControlName]="'point'" required
                                    [placeholder]="'ระบุแต้ม'">
                            </mat-form-field>
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-1/3">
                                <mat-label>รหัสประเภทโปรโมชั่น</mat-label>
                                <mat-select [formControlName]="'type'" [placeholder]="'ระบุเภทโปรโมชั่น'">
                                    <mat-option *ngFor="let item of type; let i = index;"
                                        [value]="item.id">{{item.name}}</mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field class="w-full"  >
                            <mat-date-range-input  [rangePicker]="picker" (click)="picker.open()">
                                <input matStartDate formControlName="startDate" placeholder="วันที่เริ่มต้น" (click)="picker.open()">
                                <input matEndDate formControlName="endDate" placeholder="วันที่สิ้นสุด"(click)="picker.open()" (dateChange)="changeDate()">
                              </mat-date-range-input>
                            <mat-datepicker-toggle matIconSuffix [for]="picker" ></mat-datepicker-toggle>
                            <mat-date-range-picker #picker></mat-date-range-picker>
                        </mat-form-field>
                    </div>
                    <!--<div class="md:w-full">
                        <mat-radio-group aria-label="Select an option" formControlName="isActive">
                            <mat-radio-button [value]="true" [color]="'primary'">เปิดใช้งาน</mat-radio-button>
                            <mat-radio-button [value]="false" [color]="'primary'">ปิดใช้งาน</mat-radio-button>
                        </mat-radio-group>
                    </div>-->
                    <div class="md:w-full">
                        <mat-slide-toggle class="mr-4" formControlName="isMember" color="primary">เฉพาะสมาชิก</mat-slide-toggle>
                        <mat-slide-toggle formControlName="isActive" color="primary">แสดง</mat-slide-toggle>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- <div> check : {{this.form.value | json}}</div> -->
    <div mat-dialog-actions class="flex justify-end">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
            ตกลง
        </button>
        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
            ยกเลิก
        </button>
    </div>
</div>
