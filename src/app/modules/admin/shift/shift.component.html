<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
    <div class="flex flex-row justify-between pb-2 my-5">
      <div>
        <h2 class="text-2xl md:text-xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
          รายการกะทำงาน
        </h2>
      </div>
    </div>
    <div>
      <!-- <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <mat-form-field appearance="outline">
          <mat-label>วันเริ่มต้น</mat-label>
          <input matInput [matDatepicker]="startPicker" [(ngModel)]="startDate">
          <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
          <mat-datepicker #startPicker></mat-datepicker>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>วันสิ้นสุด</mat-label>
          <input matInput [matDatepicker]="endPicker" [(ngModel)]="endDate">
          <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
          <mat-datepicker #endPicker></mat-datepicker>
        </mat-form-field>
      </div> -->

      <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger"
        class="row-border hover whitespace-nowrap overflow-auto w-full">
      </table>
    </div>
  </div>
</div>

<ng-template #btNg let-data="adtData">
  <button mat-icon-button [color]="'primary'" [matMenuTriggerFor]="menu">
    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cog-8-tooth'"></mat-icon>
  </button>
  <mat-menu #menu="matMenu">
    <button mat-menu-item (click)="openDialogEdit(data)">
      <mat-icon [svgIcon]="'heroicons_solid:pencil-square'"></mat-icon>
      <span>ดูข้อมูล</span>
    </button>
    <mat-divider></mat-divider>
    <button mat-menu-item (click)="clickDelete(data.id)">
      <mat-icon [svgIcon]="'heroicons_solid:trash'"></mat-icon>
      <span>ลบ</span>
    </button>
  </mat-menu>
</ng-template>

<ng-template #textStatus let-data="adtData">
  <div *ngIf="data.isActive; else inactiveTemplate">
    <span class="bg-green-300 rounded-xl p-2 font-semibold">เปิดใช้งาน</span>
  </div>
  <ng-template #inactiveTemplate>
    <div>
      <span class="bg-red-300 rounded-xl p-2 font-semibold">ปิดใช้งาน</span>
    </div>
  </ng-template>
</ng-template>