import { CommonModule, DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, inject, OnInit, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { ShiftService } from './shift.service';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { SummaryComponent } from './summary/summary.component';
@Component({
    selector: 'app-shift-promotion',
    standalone: true,
    imports: [
        CommonModule,
        DataTablesModule,
        MatButtonModule,
        MatMenuModule,
        MatDividerModule,
        MatDatepickerModule,
        FormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatIconModule,
    ],
    templateUrl: './shift.component.html',
    styleUrl: './shift.component.scss',
    changeDetection: ChangeDetectionStrategy.Default,
    providers: [DatePipe],
})
export class ShiftComponent implements OnInit, AfterViewInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    startDate: any;
    endDate: any;
    @ViewChild('btNg') btNg: any;
    @ViewChild('textStatus') textStatus: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;

    readonly dialog = inject(MatDialog);
    readonly fuseConfirmationService = inject(FuseConfirmationService);
    readonly toastr = inject(ToastrService);
    readonly datePipe = inject(DatePipe);
    readonly _service = inject(ShiftService);

    constructor() { }

    ngOnInit(): void {
        setTimeout(() =>
            this.loadTable());
    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }

    loadTable(): void {
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,
            ajax: (dataTablesParameters: any, callback) => {
                this._service.datatable(dataTablesParameters).subscribe({
                    next: (resp: any) => {
                        callback({
                            recordsTotal: resp.meta.totalItems,
                            recordsFiltered: resp.meta.totalItems,
                            data: resp.data
                        });
                    }, error: () => {
                        this.toastr.error('เกิดข้อผิดพลาด')
                    }
                })
            },
            order: [[1, 'desc']],
            columns: [
                {
                    title: 'ลำดับ',
                    data: 'no',
                    className: 'w-15 text-center',
                    orderable: false,
                },
                {
                    title: 'วันที่เริ่มต้น',
                    data: 'startShift',
                    // className: 'w-30',
                    ngPipeInstance: this.datePipe,
                    ngPipeArgs: ['dd-MM-yyyy HH:mm:ss'],
                    className: 'text-center',
                },
                {
                    title: 'วันที่สิ้นสุด',
                    data: 'endShift',
                    // className: 'w-30',
                    ngPipeInstance: this.datePipe,
                    ngPipeArgs: ['dd-MM-yyyy HH:mm:ss'],
                    className: 'text-center',
                },
                {
                    title: 'เงินสด',
                    data: 'cash',
                    className: 'text-center'
                },
                {
                    title: 'ชื่อผู้ใช้งาน',
                    data: 'user.firstName',
                    className: 'text-center',
                    defaultContent: '-'
                },
                {
                    title: 'อุปกรณ์',
                    data: 'device.name',
                    className: 'text-center',
                    defaultContent: '-'
                },
                {
                    title: 'สถานะ',
                    data: 'status',
                    render: (data: any) => {
                        if (data == 'open')
                            return 'เปิดกะ'
                        else if (data == 'closed')
                            return 'ปิดกะ'
                        else
                            return data
                    },
                    className: 'text-center'
                },
                // {
                //     title: 'change',
                //     data: 'change',
                //     className: 'text-center'
                // },
                {
                    title: 'จัดการ',
                    data: null,
                    defaultContent: '',
                    ngTemplateRef: {
                        ref: this.btNg,
                    },
                    className: 'w-15'
                }

            ]
        }
    }



    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(this.dtOptions);
        });
    }

    openDialogEdit(item: any) {
        this._service.getSummary(item.id).subscribe((resp: any) => {
            const DialogRef = this.dialog.open(SummaryComponent, {
                // disableClose: true,
                width: '500px',
                height: 'auto',
                enterAnimationDuration: 300,
                exitAnimationDuration: 300,
                data: {
                    data: resp
                }
            });

            DialogRef.afterClosed().subscribe((result) => {
                if (result) {
                    console.log(result, 'result')
                    this.rerender();
                }
            });
        })

    }
}
