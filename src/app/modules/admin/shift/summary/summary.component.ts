import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogActions, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';

@Component({
  selector: 'asha-summary',
  standalone: true,
  imports: [
    MatDialogTitle,
    MatDialogContent,
    MatDialogActions,
    MatButtonModule,
    CommonModule,
  ],
  template: `
    <div class="w-full overflow-auto">
      <div class="w-full flex items-center">
        <h1 mat-dialog-title class="text-xl font-semibold mb-4">Summary</h1>
      </div>
      <div mat-dialog-content class="overflow-y-auto md:max-h-180">
        <table class="w-full border-collapse border-[1px]">
          <tr>
            <th class="text-center border border-slate-600">ช่องทางชำระเงิน</th>
            <th class="text-center border border-slate-600">จำนวนรายการ</th>
            <th class="text-center border border-slate-600">จำนวนเงิน</th>
          </tr>
          <tr *ngFor="let temp of data.data">
            <td class="text-center border border-slate-600">{{temp.payment_name}}</td>
            <td class="text-center border border-slate-600">{{temp.total_transactions}}</td>
            <td class="text-center border border-slate-600">{{temp.total_amount}}</td>
          </tr>
        </table>
      </div>
      <div mat-dialog-actions class="flex justify-center">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="onClose()">
          ตกลง
        </button>
      </div>
    </div>
  `,
  styles: ``
})
export class SummaryComponent {

  readonly dialogRef = inject(MatDialogRef<SummaryComponent>);
  data: { data: any[] } = inject(MAT_DIALOG_DATA);

  constructor() { }

  ngOnInit(): void {
    
  }

  onClose() {
    this.dialogRef.close()
  }
}
