<div class="md:max-w-lg" >
    <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'NEW'">เพิ่มข้อมูล</h1>
    <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'EDIT'">แก้ไขข้อมูล</h1>
    <div mat-dialog-content class="overflow-y-auto md:max-h-180">
        <form [formGroup]="form">
            <div class="flex-auto">
                <div class="flex flex-col md:flex mb-6">
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" >
                            <mat-label>รหัสสาขา</mat-label>
                            <input matInput [placeholder]="'กรุณาระบุรหัสสาขา'" formControlName="code" [readonly]="this.data.type === 'EDIT'">
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>ชื่อสาขา</mat-label>
                            <input matInput [placeholder]="'กรุณาระบุชื่อสาขา'" formControlName="name">
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>ที่อยู่สาขา</mat-label>
                            <textarea matInput [placeholder]="'กรอกรายละเอียดที่อยู่สาขา'" [rows]="2" formControlName="address"></textarea>
                          </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>คำอธิบาย</mat-label>
                            <textarea matInput [placeholder]="'กรอกคำอธิบาย'" [rows]="2" formControlName="description"></textarea>
                        </mat-form-field>
                    </div>
                    <div class="w-full mb-4">
                        <mat-slide-toggle [formControlName]="'active'" [color]="'primary'">แสดง</mat-slide-toggle>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- <div> check : {{this.form.value | json}}</div> -->
    <div mat-dialog-actions class="flex justify-end">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
            ตกลง
        </button>
        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
            ยกเลิก
        </button>
    </div>
</div>
