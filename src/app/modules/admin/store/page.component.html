<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto p-4 m-4 rounded-md md:p-5 bg-card">
    <div class="flex flex-row justify-between pb-2">
      <div>
        <h2 class="text-2xl font-extrabold leading-7 tracking-tight truncate md:text-xl sm:leading-10 md:px-6">
          ข้อมูลร้านค้า
        </h2>
      </div>
    </div>
    <div class="flex-auto p-4 md:p-5">
      <div class="grid w-full gap-4 sm:grid-cols-1 md:grid-cols-1">
        <form [formGroup]="form">
          <div class="flex flex-col my-2">
            <div class="mb-6 -mx-3 md:flex">
              <div class="px-3 md:w-full">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>ชื่อร้านค้า</mat-label>
                  <input matInput [placeholder]="'กรอกหน่วยนับ'" formControlName="name">
                </mat-form-field>
              </div>
            </div>
            <div class="mb-6 -mx-3 md:flex">
              <div class="px-3 mb-6 md:min-w-full md:mb-0">
                <mat-form-field class="w-full">
                  <mat-label>ที่อยู่ร้านค้า</mat-label>
                  <textarea matInput [placeholder]="'กรอกรายละเอียดที่อยู่'" [rows]="3"
                    formControlName="address"></textarea>
                </mat-form-field>
              </div>
            </div>
            <div class="mb-6 -mx-3 md:flex">
              <div class="px-3 md:w-full">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>เลขประจำตัวผู้เสียภาษี</mat-label>
                  <input matInput [placeholder]="'กรอกเลข'" formControlName="tax">
                </mat-form-field>
              </div>
            </div>

            <div class="flex justify-center">
              <asha-image-upload [initial]="store?.logoUrl" (uploadSuccess)="uploadSuccess($event)" accept="image/*"></asha-image-upload>
            </div>

            <div mat-dialog-actions class="flex justify-end mt-2">
              <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
                ตกลง
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<app-page-branch [storeId]="storeId"></app-page-branch>
