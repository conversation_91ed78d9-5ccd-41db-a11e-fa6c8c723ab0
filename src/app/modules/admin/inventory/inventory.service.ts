import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';

@Injectable({
  providedIn: 'root'
})
export class InventoryService {
  constructor(private http: HttpClient) { }

  getInventoryByProductId() {
    return this.http.get(environment.apiUrl + '/api/inventory')
  }

  getInventoryHistoryByProductId(id: any) {
    return this.http.get(environment.apiUrl + `/api/inventory/${id}/transactions`)
  }

  getProduct() {
    return this.http.get(environment.apiUrl + '/api/product')
  }

  getInventoryByProductIdInv(productId: number) {
    let params = new HttpParams().set('productId', productId.toString());

    return this.http.get(`${environment.apiUrl}/api/inventory`, { params });
  }

  createWithdraw(data: any) {
    return this.http.post('/api/inventory/transfer', data)
  }

  getBranches() {
    return this.http.get(environment.apiUrl + '/api/branch')
  }

  getCategories() {
    return this.http.get(environment.apiUrl + '/api/category')
  }

  updateMinStock(id: any, data: any) {
    return this.http.put(environment.apiUrl + `/api/inventory/${id}`, data)
  }
}
