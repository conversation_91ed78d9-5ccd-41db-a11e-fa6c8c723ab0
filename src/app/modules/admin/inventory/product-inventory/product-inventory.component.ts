import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { ThaiDatePipe } from 'app/modules/common/pipe/thai-date.pipe';
import { InventoryService } from '../inventory.service';
import { MatDialog } from '@angular/material/dialog';
import { DialogForm } from '../form-dialog/dialog.component';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { orderBy } from 'lodash';
import { forkJoin } from 'rxjs';
import { MatCheckboxModule } from '@angular/material/checkbox';

@Component({
  selector: 'asha-product-inventory',
  standalone: true,
  imports: [
    CommonModule,
    ThaiDatePipe,
    RouterLink,
    MatSelectModule,
    MatFormFieldModule,
    FormsModule,
    MatInputModule,
    MatIconModule,
    MatCheckboxModule,
  ],
  providers: [
    ThaiDatePipe,
  ],
  templateUrl: './product-inventory.component.html',
  styleUrl: './product-inventory.component.scss'
})
export class ProductInventoryComponent {
  inventory: any[] = [];

  id: any;

  categories: any[] = [];
  selectedBranch: any = 0;
  selectedCategory: any = 0;

  lowStock: boolean = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private service: InventoryService,
    private toastr: ToastrService,
    private dialog: MatDialog
  ) {
    this.selectedBranch = +localStorage.getItem('branch');
  }

  ngOnInit(): void {
    forkJoin({
      categories: this.service.getCategories()
    }).subscribe({
      next: ({ categories }) => {
        this.categories = [{ id: 0, name: 'ทั้งหมด' }, ...categories as any[]];

        this.loadInventory();
      },
      error: (err) => {
        this.toastr.error('ไม่สามารถโหลดข้อมูลได้');
      }
    });
  }

  loadInventory() {
    this.service.getInventoryByProductId().subscribe({
      next: (resp: any) => {
        this.inventory = orderBy(resp, 'product.code');
      },
      error: (err) => {
        this.toastr.error('เกิดข้อผิดพลาด')
      }
    });
  }

  openDailogWithdraw() {
    const DialogRef = this.dialog.open(DialogForm, {
      disableClose: true,
      width: '500px',
      height: 'auto',
      maxHeight: '100vh',
      enterAnimationDuration: 300,
      exitAnimationDuration: 300,
      data: {
        type: 'NEW'
      }
    });
    DialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.loadInventory()
      }
    });
  }

  get inventoryList() {
    const filtered = this.inventory.filter(item => {
      const matchCategory = this.selectedCategory === 0 || item.product.category.id === this.selectedCategory;
      const matchBranch = this.selectedBranch === 0 || item.branch.id === this.selectedBranch;
      return matchCategory && matchBranch;
    });

    return filtered.filter(item => {
      return !this.lowStock || item.currentStock < item.minStock;
    });
  }

  updateMinStock(item: any) {
    const body = {
      minStock: item.minStock
    }

    this.service.updateMinStock(item.id, body).subscribe({
      next: (resp: any) => {
        this.toastr.success('บันทึกข้อมูลสำเร็จ');
      },
      error: (err) => {
        this.toastr.error('เกิดข้อผิดพลาด');
      }
    });
  }

}
