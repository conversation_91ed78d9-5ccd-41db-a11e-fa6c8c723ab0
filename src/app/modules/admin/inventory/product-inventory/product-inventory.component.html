<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto sm:p-6 bg-white dark:bg-gray-800 shadow-lg m-4 p-6 rounded-xl">
    <div class="flex flex-row justify-between items-center pb-6 mb-6 border-b border-gray-200 dark:border-gray-700">
      <div>
        <h2 class="text-2xl md:text-3xl font-bold text-gray-800 dark:text-white">
          สินค้าคงเหลือ
        </h2>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">รายการสินค้าคงเหลือทั้งหมดในระบบ</p>
      </div>
      <div>
        <button class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition duration-200"
          (click)="openDailogWithdraw()">
          <i class="fas fa-plus mr-2"></i>เบิกสินค้า
        </button>
      </div>
    </div>

    <div class="flex w-full gap-2">
      <mat-form-field class="w-2/12">
        <mat-label>หมวดหมู่</mat-label>
        <mat-select placeholder="เลือกหมวดหมู่" [(ngModel)]="selectedCategory" (selectionChange)="loadInventory()">
          <mat-option *ngFor="let item of categories" [value]="item.id">
            {{item.name}}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <mat-checkbox [(ngModel)]="lowStock" (change)="loadInventory()" color="primary">
        แสดงเฉพาะสินค้าคงเหลือน้อย
      </mat-checkbox>
    </div>

    <div class="overflow-x-auto">
      <!-- inventory-table.component.html -->
      <table class="inventory-table">
        <thead>
          <tr>
            <th>#</th>
            <th>รหัสสินค้า</th>
            <th>ชื่อสินค้า</th>
            <th>หมวดหมู่</th>
            <th>สาขา</th>
            <th>คงเหลือ</th>
            <th>จำนวนขั้นต่ำ</th>
            <th>วันที่ล่าสุด</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of inventoryList; let i = index">
            <td>{{i + 1}}</td>
            <td>{{item.product.code}}</td>
            <td>
              <a class="product-link" [routerLink]="['/inventory/' + item.id]" routerLinkActive="router-link-active">
                {{item.product.name}}
              </a>
            </td>
            <td>{{item.product.category?.name}}</td>
            <td>{{item.branch.name}}</td>
            <td>
              <span [class.low-stock]="item.currentStock < item.minStock"
                [class.enough-stock]="item.currentStock >= item.minStock" class="stock-amount">
                {{item.currentStock | number:'1.0-0'}}
              </span>
            </td>
            <td>
              <input type="number" [name]="i + minStock" [id]="i + minStock" [(ngModel)]="item.minStock">
              <button type="button" (click)="updateMinStock(item)">
                <mat-icon class="icon-size-5">save</mat-icon>
              </button>
            </td>
            <td>{{item.lastUpdated | thaidate:'dd/MM/yyyy HH:mm:ss'}}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
