:host {
  .inventory-table {
    @apply w-full border-collapse;

    thead {
      tr {
        @apply bg-gray-100 dark:bg-gray-700;

        th {
          @apply py-3 px-4 text-left text-gray-600 dark:text-gray-300 font-semibold;

          &:first-child {
            @apply rounded-tl-lg;
          }

          &:last-child {
            @apply rounded-tr-lg;
          }
        }
      }
    }

    tbody {
      @apply divide-y divide-gray-200 dark:divide-gray-700;

      tr {
        @apply hover:bg-gray-50 dark:hover:bg-gray-700/50;

        td {
          @apply py-3 px-4 text-gray-700 dark:text-gray-300;

          .product-link {
            @apply text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition duration-150;
          }

          .stock-amount {
            @apply font-medium;

            &.low-stock {
              @apply text-red-500;
            }

            &.enough-stock {
              @apply text-green-500;
            }
          }
        }
      }
    }
  }
}