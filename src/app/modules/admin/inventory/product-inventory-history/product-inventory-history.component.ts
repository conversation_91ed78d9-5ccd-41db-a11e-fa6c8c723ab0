import { Component } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { InventoryService } from '../inventory.service';
import { CommonModule } from '@angular/common';
import { ThaiDatePipe } from 'app/modules/common/pipe/thai-date.pipe';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'asha-product-inventory-history',
  standalone: true,
  imports: [
    CommonModule,
    ThaiDatePipe,
    RouterLink,
    MatButtonModule,
    MatIconModule,
  ],
  providers: [
    ThaiDatePipe,
  ],
  templateUrl: './product-inventory-history.component.html',
  styleUrl: './product-inventory-history.component.scss'
})
export class ProductInventoryHistoryComponent {
  inventory: any[] = [];

  id: any;

  constructor(
    private activatedRoute: ActivatedRoute,
    private service: InventoryService,
    private toastr: ToastrService,
  ) { }

  ngOnInit(): void {
    this.id = this.activatedRoute.snapshot.params.id;

    this.loadInventory();
  }

  loadInventory() {
    this.service.getInventoryHistoryByProductId(this.id).subscribe({
      next: (resp: any) => {
        this.inventory = resp;
      },
      error: (err) => {
        this.toastr.error('เกิดข้อผิดพลาด')
      }
    });
  }
}
