<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto sm:p-6 bg-white dark:bg-gray-800 shadow-lg m-4 p-6 rounded-xl">
    <div class="flex flex-row justify-between items-center pb-6 mb-6 border-b border-gray-200 dark:border-gray-700">
      <div>
        <h1 class="text-2xl md:text-3xl font-bold text-gray-800 dark:text-white mb-2">
          ประวัติการเปลี่ยนแปลง
        </h1>
        <a [routerLink]="['/product/edit/' + inventory[0]?.inventory?.product?.id]" routerLinkActive="router-link-active" >
          <h2 class="text-xl md:text-2xl font-semibold text-blue-600 dark:text-blue-400 underline">
            {{inventory[0]?.inventory?.product?.name}}
          </h2>
        </a>
      </div>
      <div>
        <a routerLink="/inventory" class="flex items-center text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition duration-150">
          <i class="fas fa-arrow-left mr-2"></i>ย้อนกลับ
        </a>
      </div>
    </div>

    <div class="overflow-x-auto">
      <table class="w-full border-collapse">
        <thead>
          <tr class="bg-gray-100 dark:bg-gray-700">
            <th class="py-3 px-4 text-left text-gray-600 dark:text-gray-300 font-semibold rounded-tl-lg">#</th>
            <th class="py-3 px-4 text-left text-gray-600 dark:text-gray-300 font-semibold">วันที่ทำรายการ</th>
            <th class="py-3 px-4 text-left text-gray-600 dark:text-gray-300 font-semibold">เลขที่อ้างอิง</th>
            <th class="py-3 px-4 text-left text-gray-600 dark:text-gray-300 font-semibold">ประเภท</th>
            <th class="py-3 px-4 text-left text-gray-600 dark:text-gray-300 font-semibold">จำนวน</th>
            <th class="py-3 px-4 text-left text-gray-600 dark:text-gray-300 font-semibold">คงเหลือก่อน</th>
            <th class="py-3 px-4 text-left text-gray-600 dark:text-gray-300 font-semibold">คงเหลือหลัง</th>
            <th class="py-3 px-4 text-left text-gray-600 dark:text-gray-300 font-semibold rounded-tr-lg">หมายเหตุ</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
          <tr *ngFor="let item of inventory; let i = index" class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{i + 1}}</td>
            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{item.transactionDate | thaidate:'dd/MM/yyyy HH:mm:ss'}}</td>
            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{item.referenceNo}}</td>
            <td class="py-3 px-4">
              <span [class.text-green-500]="item.type === 'stock_in'" 
                    [class.text-red-500]="item.type === 'stock_out'"
                    class="font-medium">
                {{item.type}}
              </span>
            </td>
            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{item.quantity | number:'1.0-0'}}</td>
            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{item.stockBefore | number:'1.0-0'}}</td>
            <td class="py-3 px-4 font-medium text-gray-700 dark:text-gray-300">{{item.stockAfter | number:'1.0-0'}}</td>
            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">{{item.notes || '-'}}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>