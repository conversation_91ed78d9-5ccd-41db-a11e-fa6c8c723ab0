<h2 mat-dialog-title class="text-lg font-bold">บันทึกการเบิกสินค้า</h2>

<form [formGroup]="form" class="p-4 space-y-4">
    <!-- product -->
    <app-dropdown-search-lazy fieldClass="w-full fuse-mat-dense" formControlName="productId" [items]="products"
        [config]="productConfig" (selected)="selecetProduct($event.id)"
        [showClearButton]="false" [label]="'เลือกสินค้า'"></app-dropdown-search-lazy>
    <!-- คลังต้นทาง -->
    <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>คลังต้นทาง</mat-label>
        <mat-select formControlName="fromInventoryId" required>
            <mat-option *ngFor="let inv of inventoryListFrom" [value]="inv.id">
                {{ inv.branch.name }} [คงเหลือ : {{inv.availableStock}}]
            </mat-option>
        </mat-select>
    </mat-form-field>

    <!-- คลังปลายทาง -->
    <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>คลังปลายทาง</mat-label>
        <mat-select formControlName="toInventoryId" required>
            <mat-option *ngFor="let inv of inventoryListTo" [value]="inv.id">
                {{ inv.branch.name }} [คงเหลือ : {{inv.availableStock}}]
            </mat-option>
        </mat-select>
    </mat-form-field>

    <!-- จำนวน -->
    <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>จำนวน</mat-label>
        <input matInput type="number" formControlName="quantity" min="1" required />
    </mat-form-field>

    <!-- หมายเหตุ -->
    <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>หมายเหตุ</mat-label>
        <textarea matInput rows="3" formControlName="notes"></textarea>
    </mat-form-field>
    <!-- เลขที่อ้างอิง -->
    <!-- ปุ่ม -->
    <div class="flex justify-end gap-2 pt-4">
        <button mat-stroked-button mat-dialog-close>ยกเลิก</button>
        <button mat-flat-button color="primary" (click)="onSubmit()">
            บันทึก
        </button>
    </div>
</form>