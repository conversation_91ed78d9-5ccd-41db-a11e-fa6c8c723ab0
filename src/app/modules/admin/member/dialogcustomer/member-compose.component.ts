import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataTablesModule } from 'angular-datatables';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import {
    MatDialog,
    MatDialogTitle,
    MatDialogContent,
    MatDialogActions,
    MatDialogClose,
    MatDialogRef,
    MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';
import { MatToolbarModule } from '@angular/material/toolbar';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { ToastrService } from 'ngx-toastr';
import { MatRadioModule } from '@angular/material/radio';
import { MemberService } from '../member.service';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DateTime } from 'luxon';
@Component({
    selector: 'app-member-compose',
    standalone: true,
    templateUrl: './member-compose.component.html',
    styleUrl: './member-compose.component.scss',
    imports: [
        CommonModule,
        DataTablesModule,
        MatIconModule,
        MatFormFieldModule,
        MatInputModule,
        FormsModule,
        MatToolbarModule,
        MatButtonModule,
        MatDialogTitle,
        MatDialogContent,
        MatDialogActions,
        MatDialogClose,
        MatSelectModule,
        ReactiveFormsModule,
        MatInputModule,
        MatFormFieldModule,
        MatRadioModule,
        MatSlideToggleModule,
        MatDatepickerModule
    ]
})
export class MemberComposeComponent implements OnInit {

    form: FormGroup;
    stores: any[] = [];
    category: any[] = [
        'Student',
        'Staff',
        'Teacher',
    ];
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    dtOptions: DataTables.Settings = {};
    addForm: FormGroup;
    grades: any
    constructor(
        private dialogRef: MatDialogRef<MemberComposeComponent>,
        @Inject(MAT_DIALOG_DATA) public data: any,
        public dialog: MatDialog,
        private FormBuilder: FormBuilder,
        public _service: MemberService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
    ) {
        console.log(this.data);
        
        if (this.data.type === 'EDIT') {
            this.form = this.FormBuilder.group({
                code: this.data.value.code ?? '',
                firstname: this.data.value.firstname ?? '',
                middlename: this.data.value.middlename ?? '',
                lastname: this.data.value.lastname ?? '',
                active: this.data.value.active,
                cardSN: this.data.value.sn,
                cardType: this.data.value.cardType ?? '',
                activeDate: this.data.value.activeDate ?? '',
                gradeId: this.data.value.grade?.id,
                limitcredit: this.data.value.limitcredit
            });
        } else {
            this.form = this.FormBuilder.group({
                code: '',
                firstname: '',
                middlename: '',
                lastname: '',
                active: true,
                cardSN: null,
                cardType: ['',[Validators.required]],
                activeDate: '',
                gradeId: null,
                limitcredit: 0
            });
        }
        this._service.getGrade().subscribe((resp: any) => {
            this.grades = resp
            console.log('this.grades', this.grades);
            
            //console.log('Grade resp: ',resp[0]?.id);
        })
    }

    ngOnInit(): void {
        if (this.data.type === 'EDIT') {
        } else {
        }
    }

    onChangeGrade(){
        const grade = this.grades.find(item=> item.id == this.form.value.gradeId)
        console.log('grade',grade);
        this.form.patchValue({
            limitcredit: grade.limitcredit ?? 0
        })
    }

    Submit() {
        let formValue = this.form.value
        //if(formValue.gradeId == '')
        //    formValue.gradeId = 0
        formValue.activeDate = DateTime.fromISO(formValue.activeDate).toFormat('yyyy-MM-dd');
        const confirmation = this.fuseConfirmationService.open({
            title: "ยืนยันการบันทึกข้อมูล",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "primary"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "ยืนยัน",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "ยกเลิก"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    if (this.data.type === 'NEW') {
                        this._service.create(formValue).subscribe({
                            error: (err) => {
                                this.toastr.error('ไม่สามารถบันทึกข้อมูลได้')
                            },
                            //error: (err) => {
                            //    this.fuseConfirmationService.open({
                            //        title: 'กรุณาตรวจสอบข้อมูล',
                            //        message: err.error.message,
                            //        icon: {
                            //            show: true,
                            //            name: 'heroicons_outline:exclamation',
                            //            color: 'warning',
                            //        },
                            //        actions: {
                            //            confirm: {
                            //                show: false,
                            //                label: 'ยืนยัน',
                            //                color: 'warn',
                            //            },
                            //            cancel: {
                            //                show: false,
                            //                label: 'ยกเลิก',
                            //            },
                            //        },
                            //        dismissible: true,
                            //    });
                            //},
                            complete: () => {
                                this.toastr.success('ดำเนินการเพิ่มข้อมูลสำเร็จ')
                                this.dialogRef.close(true)
                            },
                        });
                    } else {
                        this._service.update(this.data.value.id, formValue).subscribe({
                            error: (err) => {
                                this.fuseConfirmationService.open({
                                    title: 'กรุณาตรวจสอบข้อมูล',
                                    message: err.error.message,
                                    icon: {
                                        show: true,
                                        name: 'heroicons_outline:exclamation',
                                        color: 'warning',
                                    },
                                    actions: {
                                        confirm: {
                                            show: false,
                                            label: 'ยืนยัน',
                                            color: 'warn',
                                        },
                                        cancel: {
                                            show: false,
                                            label: 'ยกเลิก',
                                        },
                                    },
                                    dismissible: true,
                                });
                            },
                            complete: () => {
                                this.toastr.success('ดำเนินการแก้ไขข้อมูลสำเร็จ')
                                this.dialogRef.close(true)
                            },
                        });
                    }
                }
            }
        )
    }

    onClose() {
        this.dialogRef.close()
    }

}
