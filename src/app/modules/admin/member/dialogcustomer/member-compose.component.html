<div class="md:max-w-lg">
    <h1 mat-dialog-title class="mb-4 text-xl font-semibold" *ngIf="this.data?.type === 'NEW'">เพิ่มข้อมูล</h1>
    <h1 mat-dialog-title class="mb-4 text-xl font-semibold" *ngIf="this.data?.type === 'EDIT'">แก้ไขข้อมูล</h1>
    <div mat-dialog-content class="overflow-y-auto">
        <form [formGroup]="form">
            <div class="flex-auto">
                <div class="flex flex-col mb-6 md:flex">
                    <div class="pt-5 md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" appearance="outline">
                            <mat-label>รหัสพนักงาน</mat-label>
                            <input matInput [placeholder]="'กรอกรหัสพนักงาน'" formControlName="code">
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" appearance="outline">
                            <mat-label>Grade</mat-label>
                            <mat-select [formControlName]="'gradeId'" (selectionChange)="onChangeGrade()">
                                <mat-option *ngFor="let grade of grades;" [value]="grade?.id">
                                    {{grade?.name}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" appearance="outline">
                            <mat-label>ชื่อ</mat-label>
                            <input matInput [placeholder]="'กรุณาระบุชื่อ'" formControlName="firstname">
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" appearance="outline">
                            <mat-label>ชื่อกลาง</mat-label>
                            <input matInput [placeholder]="'กรุณาระบุชื่อกลาง'" formControlName="middlename">
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" appearance="outline">
                            <mat-label>นามสกุล</mat-label>
                            <input matInput [placeholder]="'กรุณาระบุนามสกุล'" formControlName="lastname">
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" appearance="outline">
                            <mat-label>limit credit</mat-label>
                            <input matInput [placeholder]="'กรุณาระบุ limit credit'" formControlName="limitcredit">
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" appearance="outline">
                            <mat-label>Card SN.</mat-label>
                            <input matInput [placeholder]="'กรุณาระบุ Card SN.'" formControlName="cardSN">
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" appearance="outline">
                            <mat-label>ประเภท Card</mat-label>
                            <mat-select [formControlName]="'cardType'">
                                <mat-option *ngFor="let item of category;" [value]="item">
                                    {{item}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" appearance="outline">
                            <mat-label>วันที่ Active Card</mat-label>
                            <input matInput [matDatepicker]="picker" placeholder="กรุณาระบุวันที่" formControlName="activeDate">
                            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                            <mat-datepicker #picker></mat-datepicker>
                        </mat-form-field>
                    </div>
                    <div class="w-full mb-4">
                        <mat-slide-toggle formControlName="active" color="primary">แสดง</mat-slide-toggle>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div mat-dialog-actions class="flex justify-end">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
            ตกลง
        </button>
        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
            ยกเลิก
        </button>
    </div>
</div>
