import { CommonModule, CurrencyPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, OnInit, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { FilePickerModule } from 'ngx-awesome-uploader';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { DialogRef } from '@angular/cdk/dialog';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { PictureComponent } from '../picture/picture.component';
import { ProductComposeComponent } from '../product/dialog/product-compose/product-compose.component';
import { MemberService } from './member.service';
import { MemberComposeComponent } from './dialogcustomer/member-compose.component';
import { DialogForm } from './form-dialog/dialog.component';
import { DialogCreditComponent } from './dialog-credit/dialog-credit.component';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
@Component({
    selector: 'app-member',
    standalone: true,
    imports: [
        CommonModule,
        DataTablesModule,
        MatButtonModule,
        MatIconModule,
        FilePickerModule,
        MatMenuModule,
        MatDividerModule,
        MatSelectModule,
        MatInputModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule
    ],
    providers: [
        CurrencyPipe
    ],
    templateUrl: './member.component.html',
    styleUrl: './member.component.scss',
    changeDetection: ChangeDetectionStrategy.Default,
})
export class MemberComponent implements OnInit, AfterViewInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    form: FormGroup
    @ViewChild('textStatus') textStatus: any;
    @ViewChild('btNg') btNg: any;
    @ViewChild('btPicture') btPicture: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;
    category: any[] = [
        'Student',
        'Staff',
        'Teacher',
    ];
    filter_cardType: any
    constructor(
        private _service: MemberService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        public dialog: MatDialog,
        private currencyPipe: CurrencyPipe,
        private _router: Router,
        private _fb: FormBuilder

    ) {
    this.form = this._fb.group({
        card_type: '',
    })
    }
    ngOnInit(): void {
        setTimeout(() =>
            this.loadTable());

    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }

    onChangeType() {
        console.log('filter_cardType',this.filter_cardType);
        
        this.rerender()
    }

    loadTable(): void {
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,     // Set the flag
            scrollX: true,
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.filter = {
                    'filter.member.card_type': this.filter_cardType ?? '',
                }
                this._service?.datatable(dataTablesParameters).subscribe({
                    next: (resp: any) => {
                        callback({
                            recordsTotal: resp?.meta?.totalItems,
                            recordsFiltered: resp?.meta?.totalItems,
                            data: resp?.data
                        });
                    },error: () => {
                        this.toastr.error('เกิดข้อผิดพลาด')
                    }
                })
            },
            columns: [
                {
                    title: 'ลำดับ',
                    data: 'no',
                    className: 'w-15 text-center'
                },
                {
                    title: 'รหัสพนักงาน',
                    data: 'code',
                    className: 'w-30 text-center'
                },
                {
                    title: 'ชื่อ',
                    data: 'firstname',
                    className: 'text-center'
                },
                {
                    title: 'ชื่อกลาง',
                    data: 'middlename',
                    render:(data)=>{
                        if(!data){
                            return '-';
                        }
                        else{
                            return data;
                        }
                    },
                    className: 'text-center'
                },
                {
                    title: 'นามสกุล',
                    data: 'lastname',
                    className: 'text-center'
                },
                {
                    title: 'Card SN.',
                    defaultContent: '-',
                    data: 'sn',
                    className: 'text-center'
                },
                {
                    title: 'Card Type',
                    defaultContent: '-',
                    data: 'cardType',
                    className: 'text-center'
                },
                {
                    title: 'Grade',
                    defaultContent: '-',
                    data: 'grade.name',
                    className: 'text-center'
                },
                //{
                //    title: 'OT Credit',
                //    data: 'creditEL2',
                //    ngPipeInstance: this.currencyPipe,
                //    ngPipeArgs: ['THB','symbol','1.0-0'],
                //    className: 'text-center'
                //},
                //{
                //    title: 'Persanal Wallet',
                //    data: 'wallet',
                //    ngPipeInstance: this.currencyPipe,
                //    ngPipeArgs: ['THB','symbol','1.0-0'],
                //    className: 'text-center'
                //},
                {
                    title: 'ยอดที่ใช้',
                    data: 'credit',
                    ngPipeInstance: this.currencyPipe,
                    ngPipeArgs: ['THB','symbol','1.0-0'],
                    className: 'text-center'
                },
                {
                    title: 'ยอดคงเหลือ',
                    data: null,
                    render: (data) => {
                        if (data.credit !== '' && data.limitcredit !== '') {
                            return this.currencyPipe.transform(data.limitcredit - data.credit, 'THB', 'symbol', '1.0-0');
                        } else {
                            return '-';  // Return '-' directly here
                        }
                    },
                    className: 'text-center'
                },
                {
                    title: 'วงเงิน',
                    data: 'defaultcredit',
                    ngPipeInstance: this.currencyPipe,
                    ngPipeArgs: ['THB','symbol','1.0-0'],
                    className: 'text-center'
                },
                {
                    title: 'เพิ่มวงเงินชั่วคราว',
                    data: 'limitcredit',
                    ngPipeInstance: this.currencyPipe,
                    ngPipeArgs: ['THB','symbol','1.0-0'],
                    className: 'text-center'
                },
                {
                    title: 'แสดง',
                    data: null,
                    defaultContent: '',
                    ngTemplateRef: {
                        ref: this.textStatus,
                    },
                    className: 'w-30 text-center'
                },
                //{
                //    title: 'VIP Credit',
                //    data: 'creditEL4',
                //    ngPipeInstance: this.currencyPipe,
                //    ngPipeArgs: ['THB','symbol','1.0-0'],
                //    className: 'text-center'
                //},
                {
                    title: 'จัดการ',
                    data: null,
                    defaultContent: '',
                    ngTemplateRef: {
                        ref: this.btNg,
                    },
                    className: 'w-15 text-center',

                }

            ]
        }
    }

    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(this.dtOptions);
        });
    }

    opendialogapro2() {
        const DialogRef = this.dialog.open(DialogForm, {
            disableClose: true,
            width: '500px',
            height: 'auto',
            enterAnimationDuration: 300,
            exitAnimationDuration: 300,
            data: {
                type: 'NEW'
            }
        });
        DialogRef.afterClosed().subscribe((result) => {
            if (result) {
                console.log(result, 'result')
                this.rerender();
            }
        });
    }

    opendialogapro() {
        const DialogRef = this.dialog.open(MemberComposeComponent, {
            disableClose: true,
            width: '500px',
            height: 'auto',
            enterAnimationDuration: 300,
            exitAnimationDuration: 300,
            data: {
                type: 'NEW'
            }
        });
        DialogRef.afterClosed().subscribe((result) => {
            if (result) {
                console.log(result, 'result')
                this.rerender();
            }
        });
    }

    openDialogEdit(item: any) {
        const DialogRef = this.dialog.open(MemberComposeComponent, {
            disableClose: true,
            width: '500px',
            enterAnimationDuration: 300,
            exitAnimationDuration: 300,
            data: {
                type: 'EDIT',
                value: item
            }
        });
        DialogRef.afterClosed().subscribe((result) => {
            if (result) {
                console.log(result, 'result')
                this.rerender();
            }
        });
    }

    openDialoCredit(item: any) {
        const DialogRef = this.dialog.open(DialogCreditComponent, {
            disableClose: true,
            width: '500px',
            enterAnimationDuration: 300,
            exitAnimationDuration: 300,
            data: {
                type: 'EDIT',
                value: item
            }
        });
        DialogRef.afterClosed().subscribe((result) => {
            if (result) {
                console.log(result, 'result')
                this.rerender();
            }
        });
    }

    clickDelete(id: any) {
        const confirmation = this.fuseConfirmationService.open({
            title: "ยืนยันลบข้อมูล",
            message: "กรุณาตรวจสอบข้อมูล หากลบข้อมูลแล้วจะไม่สามารถนำกลับมาได้",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "warn"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "ยืนยัน",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "ยกเลิก"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    this._service.delete(id).subscribe({
                        error: (err) => {

                        },
                        complete: () => {
                            this.toastr.success('ดำเนินการลบสำเร็จ');
                            this.rerender();
                        },
                    });
                }
            }
        )
    }

    move_upGrade(){
        const confirmation = this.fuseConfirmationService.open({
            title: "ยืนยันการเลื่อนระดับชั้น",
            message: "กรุณาตรวจสอบข้อมูล หากกดยืนยันแล้วจะไม่สามารถแก้ไขได้",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "warn"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "ยืนยัน",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "ยกเลิก"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    const confirmation = this.fuseConfirmationService.open({
                        title: "ต้องการ backup ข้อมูลรึเปล่า",
                        message: "เผื่อในกรณีที่ต้องการย้อนกลับหรือเกิดการผิดพลาด",
                        icon: {
                            show: true,
                            name: "heroicons_outline:exclamation-triangle",
                            color: "warn"
                        },
                        actions: {
                            confirm: {
                                show: true,
                                label: "ยืนยัน",
                                color: "primary"
                            },
                            cancel: {
                                show: true,
                                label: "ยกเลิก"
                            }
                        },
                        dismissible: false
                    })
            
                    confirmation.afterClosed().subscribe(
                        result => {
                            if (result == 'confirmed') {
                                this._service.backUpData().subscribe({
                                    error: (err) => {
                                        this.toastr.error('เกิดข้อผิดพลาด');
                                    },
                                    complete: () => {
                                        this.toastr.success('download file backup complete');
                                        this._service.moveupGrade().subscribe({
                                            error: (err) => {
                                                this.toastr.error('เกิดข้อผิดพลาดในการเลื่อนระดับชั้น');
                                            },
                                            complete: () => {
                                                this.toastr.success('ดำเนินการเลื่อนระดับชั้นสำเร็จ');
                                                this.rerender();
                                            },
                                        });
                                    },
                                });
                            }
                        }
                    )
                    
                }
            }
        )
    }

    showPicture(imgObject: string): void {
        console.log(imgObject)
        this.dialog
            .open(PictureComponent, {
                autoFocus: false,
                data: {
                    imgSelected: imgObject,
                },
            })
            .afterClosed()
            .subscribe(() => {
                // Go up twice because card routes are setup like this; "card/CARD_ID"
                // this._router.navigate(['./../..'], {relativeTo: this._activatedRoute});
            });
    }
    
    createProduct() {
        const DialogRef = this.dialog.open(ProductComposeComponent, {
            disableClose: true,
            width: '800px',
            height: '90%',
            enterAnimationDuration: 300,
            exitAnimationDuration: 300,
            data: {
                type: 'NEW'
            }
        });
        DialogRef.afterClosed().subscribe((result) => {
            if (result) {
                console.log(result, 'result')
                this.rerender();
            }
        });
    }
}
