<div class="flex flex-col flex-auto min-w-0">
    <div class="flex-auto p-4 m-4 rounded-md sm:p-10 bg-card">
        <div class="flex flex-row justify-between pb-2 my-5">
            <div>
                <h2 class="text-2xl font-extrabold leading-7 tracking-tight truncate md:text-xl sm:leading-10">
                    รายการสมาชิก
                </h2>
            </div>

            <div class="flex ml-auto space-x-4">
                <button
                    mat-flat-button
                    color="warn"
                    (click)="move_upGrade()"
                >
                    <mat-icon
                        class="icon-size-5"
                        svgIcon="heroicons_solid:chevron-double-up"
                    ></mat-icon>
                    <span class="ml-2">เลื่อนระดับชั้น</span>
                </button>
                <button
                    mat-flat-button
                    color="primary"
                    (click)="opendialogapro2()"
                >
                    <mat-icon
                        class="icon-size-5"
                        svgIcon="heroicons_solid:plus-circle"
                    ></mat-icon>
                    <span class="ml-2">นำเข้าสมาชิก</span>
                </button>
                <button
                    mat-flat-button
                    color="primary"
                    (click)="opendialogapro()"
                >
                    <mat-icon
                        class="icon-size-5"
                        svgIcon="heroicons_solid:plus-circle"
                    ></mat-icon>
                    <span class="ml-2">เพิ่มสมาชิก</span>
                </button>
            </div>
        </div>
        <div class="md:w-1/4 w-full">
            <mat-form-field class="w-full">
                <mat-label>เลือกประเภทบัตร</mat-label>
                <mat-select
                    [(ngModel)]="filter_cardType"
                    (selectionChange)="onChangeType()"
                >
                    <mat-option [value]=""> ทั้งหมด </mat-option>
                    <mat-option
                        *ngFor="let item of category"
                        [value]="item"
                    >
                        {{ item }}
                    </mat-option>
                </mat-select>
            </mat-form-field>
        </div>

        <table
            datatable
            [dtOptions]="dtOptions"
            [dtTrigger]="dtTrigger"
            class="table w-full bg-white row-border hover whitespace-nowrap"
        ></table>
    </div>

    <ng-template #btNg let-data="adtData">
        <button mat-icon-button color="primary" [matMenuTriggerFor]="menu">
            <mat-icon
                class="icon-size-5"
                svgIcon="heroicons_solid:cog-8-tooth"
            ></mat-icon>
        </button>
        <mat-menu #menu="matMenu">
            <button mat-menu-item (click)="openDialogEdit(data)">
                <mat-icon svgIcon="heroicons_solid:pencil-square"></mat-icon>
                <span>แก้ไข</span>
            </button>
            <mat-divider></mat-divider>
            <button mat-menu-item (click)="openDialoCredit(data)">
                <mat-icon
                    svgIcon="heroicons_outline:currency-dollar"
                ></mat-icon>
                <span>ปรับวงเงินเครดิต</span>
            </button>
            <mat-divider></mat-divider>
            <button mat-menu-item (click)="clickDelete(data.id)">
                <mat-icon svgIcon="heroicons_solid:trash"></mat-icon>
                <span>ลบ</span>
            </button>
        </mat-menu>
    </ng-template>
</div>

<ng-template #textStatus let-data="adtData">
    <div *ngIf="data.active; else inactiveTemplate">
        <span class="bg-green-300 rounded-xl p-2 font-semibold"
            >เปิดใช้งาน</span
        >
    </div>
    <ng-template #inactiveTemplate>
        <div>
            <span class="bg-red-300 rounded-xl p-2 font-semibold"
                >ปิดใช้งาน</span
            >
        </div>
    </ng-template>
</ng-template>
