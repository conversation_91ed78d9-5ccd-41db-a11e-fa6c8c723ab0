<div class="md:max-w-lg">
    <h1 mat-dialog-title class="mb-4 text-xl font-semibold" *ngIf="this.data?.type === 'NEW'">เพิ่มข้อมูล</h1>
    <h1 mat-dialog-title class="mb-4 text-xl font-semibold" *ngIf="this.data?.type === 'EDIT'">แก้ไขข้อมูล</h1>
    <div mat-dialog-content class="overflow-y-auto md:max-h-180">
        <form [formGroup]="form">
            <div class="flex-auto">
                <div class="flex flex-col mb-2 md:flex">
                    <div class="md:w-full">
                      <p class="font-bold text-black">รหัสพนักงาน : {{data.value.code ?? '-'}}</p>
                      <p class="font-bold text-black">ชื่อ - นามสกุล : {{data.value.firstname}} {{data.value.lastname}}</p>
                    </div>
                    <div class="pt-5 md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" appearance="outline">
                            <mat-label>Credit</mat-label>
                            <input matInput [placeholder]="'กรุณะระบุจำนวน Credit'" [formControlName]="'limitcredit'" type="number">
                        </mat-form-field>
                    </div>
                    <div class="w-full mb-4">
                        <mat-slide-toggle formControlName="status" color="primary">ถาวร</mat-slide-toggle>
                    </div>
                    <!--<div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" appearance="outline">
                            <mat-label>ประเภท Credit</mat-label>
                            <mat-select [formControlName]="'walletType'">
                                <mat-option *ngFor="let item of category;" [value]="item.key">
                                    {{item.value}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>-->
                </div>
            </div>
        </form>
    </div>
    <div mat-dialog-actions class="flex justify-end">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
            ตกลง
        </button>
        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
            ยกเลิก
        </button>
    </div>
</div>
