<div class="md:max-w-2xl w-full overflow-auto" *ngIf="data?.type === 'EDIT'">
    <div class="grid grid-cols-1 gap-6 p-4 bg-white rounded-lg shadow-lg">
        <!-- หัวข้อหลัก -->
        <div class="col-span-1 text-center space-y-2">
            <h1 class="text-2xl font-bold text-gray-800">รายละเอียดคำสั่งซื้อ</h1>
            <div class="w-20 h-1 bg-blue-500 mx-auto"></div>
        </div>

        <!-- ข้อมูลทั่วไป -->
        <div class="col-span-1 space-y-3 bg-gray-50 p-4 rounded-lg">
            <div class="flex items-center">
                <mat-icon class="text-gray-500 mr-2">receipt</mat-icon>
                <p class="font-semibold text-gray-700">เลขที่ทำรายการ:
                    <span class="font-normal ml-1">{{ data.value.orderNo ?? '-' }}</span>
                </p>
            </div>

            <div class="flex items-center">
                <mat-icon class="text-gray-500 mr-2">event</mat-icon>
                <p class="font-semibold text-gray-700">วันที่ทำรายการ:
                    <span class="font-normal ml-1">{{ data.value.orderDate | date: 'dd/MM/yyyy HH:mm' }}</span>
                </p>
            </div>

            <div class="flex items-center">
                <mat-icon class="text-gray-500 mr-2">payment</mat-icon>
                <p class="font-semibold text-gray-700">ช่องทางการชำระเงิน:
                    <span class="font-normal ml-1">{{ data.value.orderPayment?.paymentMethod?.name ?? '-' }}</span>
                </p>
            </div>

            <div class="flex items-center">
                <mat-icon class="text-gray-500 mr-2">info</mat-icon>
                <p class="font-semibold text-gray-700">สถานะคำสั่งซื้อ:
                    <span class="font-normal ml-1 capitalize">{{ convertStatus(data.value.orderStatus) ?? '-' }}</span>
                </p>
            </div>
        </div>

        <!-- รายการสินค้า -->
        <div class="col-span-1">
            <h2 class="text-lg font-semibold text-gray-700 mb-3 flex items-center">
                <mat-icon class="mr-1">shopping_basket</mat-icon>
                รายการสินค้า
            </h2>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">
                                ลำดับ</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">
                                ชื่อสินค้า</th>
                            <th
                                class="px-4 py-2 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">
                                จำนวน</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">
                                ราคา</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr *ngFor="let item of data.value.orderItems; let i = index">
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-600">{{ i + 1 }}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-800">{{
                                item.product?.name ?? '-' }}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-center text-gray-600">{{ item.quantity
                                ?? '-' }}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-600">{{ item.total |
                                currency:'THB':'symbol':'1.2-2' }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- สรุปยอด -->
        <div class="col-span-1 bg-blue-50 p-4 rounded-lg">
            <h2 class="text-lg font-semibold text-gray-700 mb-3 flex items-center">
                <mat-icon class="mr-1">attach_money</mat-icon>
                สรุปยอดชำระ
            </h2>

            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-gray-700">รวม</span>
                    <span class="font-medium">{{ data.value.total | currency:'THB':'symbol':'1.2-2' }}</span>
                </div>

                <div class="flex justify-between">
                    <span class="text-gray-700">ส่วนลด</span>
                    <span class="text-red-500">-{{ data.value.discount | currency:'THB':'symbol':'1.2-2' }}</span>
                </div>

                <div class="border-t border-gray-300 pt-2 mt-2 flex justify-between">
                    <span class="font-semibold text-gray-800">ยอดสุทธิ</span>
                    <span class="font-bold text-blue-600">{{ data.value.grandTotal | currency:'THB':'symbol':'1.2-2'
                        }}</span>
                </div>

                <div class="flex justify-between">
                    <span class="text-gray-700">ชำระแล้ว</span>
                    <span class="font-medium text-green-600">{{ data.value.paid | currency:'THB':'symbol':'1.2-2'
                        }}</span>
                </div>

                <div class="flex justify-between">
                    <span class="text-gray-700">เงินทอน</span>
                    <span class="font-medium">{{ data.value.change | currency:'THB':'symbol':'1.2-2' }}</span>
                </div>
            </div>
        </div>

        <!-- ปุ่มปิด -->
        <div class="col-span-1 flex justify-end mt-4">
            <button mat-raised-button color="primary" (click)="onClose()" class="flex items-center">
                <mat-icon class="mr-1">close</mat-icon>
                ปิดหน้าต่าง
            </button>
        </div>
    </div>
</div>