import { CommonModule, DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, OnInit, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';

import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { FilePickerModule } from 'ngx-awesome-uploader';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { DialogRef } from '@angular/cdk/dialog';
import { DialogForm } from './form-dialog/dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { OrderService } from './order.service';
import { DateTime } from 'luxon';
import { orderBy } from 'lodash';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
@Component({
    selector: 'app-activity',
    standalone: true,
    providers: [
        DatePipe
    ],
    imports: [
        CommonModule,
        DataTablesModule,
        MatButtonModule,
        MatIconModule,
        FilePickerModule,
        MatMenuModule,
        MatDividerModule,
        FormsModule,
        ReactiveFormsModule,
        MatSelectModule,
        MatDatepickerModule,
        MatInputModule
    ],
    templateUrl: './order.component.html',
    styleUrl: './order.component.scss',
    changeDetection: ChangeDetectionStrategy.Default,
})
export class OrderComponent implements OnInit, AfterViewInit {
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();

    @ViewChild('btNg') btNg: any;
    @ViewChild('grandTotal') grandTotal: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;

    filterStatus: any[] = [
        'complete',
        'void'
    ]
    datePicked: any
    data_day: any;
    data_month: any;
    data_year: any;
    filter_status: any

    filter_device: any
    filterDevice: any
    constructor(
        private _service: OrderService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        public dialog: MatDialog,
        private _fb: FormBuilder,
        private datePipe: DatePipe
    ) {
        this._service.getDevice().subscribe({
            next: (resp: any) => {
                this.filterDevice = resp
            }
        })
    }
    ngOnInit(): void {
        setTimeout(() =>
            this.loadTable());

    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }

    formatDateTime(date: string): string {
        return DateTime.fromISO(date).toFormat('dd/MM/yyyy HH:mm:ss');
    }

    onChangeStatus() {
        this.rerender()
        console.log('filter_status', this.filter_status);
    }

    onChangeDevice() {
        this.rerender()
        console.log('filter_device', this.filter_device);
    }

    loadTable(): void {
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,     // Set the flag
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.filter = {
                    'filter.orderStatus': this.filter_status ?? '',
                    'filter.device.id': this.filter_device ?? '',
                    'filter.orderDate': (this.data_day && this.data_month && this.data_year) ? `$btw:${this.data_year}-${this.data_month}-${this.data_day} 00:00:00,${this.data_year}-${this.data_month}-${this.data_day} 23:59:59` : '',
                    'filter.orderType': 'order',
                }
                this._service.datatable(dataTablesParameters).subscribe({
                    next: (resp: any) => {
                        callback({
                            recordsTotal: resp?.meta?.totalItems,
                            recordsFiltered: resp?.meta?.totalItems,
                            data: resp?.data
                        });
                    }, error: () => {
                        this.toastr.error('เกิดข้อผิดพลาด')
                    }
                })
            },
            columns: [
                {
                    title: 'ลำดับ',
                    data: 'no',
                    defaultContent: '-',
                    className: 'w-15 text-center'
                },
                {
                    title: 'เลขที่ทำรายการ',
                    defaultContent: '-',
                    data: 'orderNo',
                    className: 'text-center'
                },
                {
                    title: 'วันที่ทำรายการ',
                    defaultContent: '-',
                    data: function (row: any) {
                        return DateTime.fromISO(row.orderDate).toFormat('dd/MM/yyyy HH:mm:ss');
                    },
                    className: 'text-center'
                },
                {
                    title: 'อุปกรณ์',
                    defaultContent: '-',
                    data: 'device.name',
                    className: 'text-center'
                },
                {
                    title: 'ช่องทางชำระ',
                    defaultContent: '-',
                    data: 'orderPayment.paymentMethod.name',
                    className: 'text-center'
                },
                {
                    title: 'ยอดรวม',
                    defaultContent: '-',
                    data: null,
                    ngTemplateRef: {
                        ref: this.grandTotal,
                    },
                    className: 'text-right'
                },
                {
                    title: 'สถานะ',
                    defaultContent: '-',
                    data: 'orderStatus',
                    render: (data: any) => {
                        if (data == 'select_payment') {
                            return 'เลือกช่องทางชำระ'
                        } else if (data == 'wait_payment') {
                            return 'รอการชำระ'
                        } else if (data == 'complete') {
                            return 'ชำระเงินสำเร็จ'
                        } else if (data == 'incomplete') {
                            return 'ชำระเงินไม่สำเร็จ'
                        } else if (data == 'void') {
                            return 'คืนเงิน'
                        } else {
                            return data
                        }
                    },
                    className: 'text-center'
                },

                {
                    title: 'จัดการ',
                    data: null,
                    defaultContent: '',
                    ngTemplateRef: {
                        ref: this.btNg,
                    },
                    className: 'w-15 text-center'
                }

            ],
            // orderBy: [[1, 'DESC']]
        }
    }

    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(this.dtOptions);
        });
    }

    filterdate() {
        console.log("datePicked: ", this.datePicked);
        this.data_day = this.datePicked ? this.datePipe.transform(this.datePicked, 'dd') : 0;
        this.data_month = this.datePicked ? this.datePipe.transform(this.datePicked, 'MM') : 12;
        this.data_year = this.datePicked ? this.datePipe.transform(this.datePicked, 'yyyy') : 2024;
        console.log(`${this.data_day}, ${this.data_month}, ${this.data_year}`);
        this.rerender()
    }

    void_order(id: any) {
        const confirmation = this.fuseConfirmationService.open({
            title: "คุณต้องการ void ใช่หรือไม่ ?",
            message: "กรุณาตรวจสอบให้แน่ใจ หากยืนยันแล้วจะไม่สามารถย้อนกลับ",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "warn"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "ยืนยัน",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "ยกเลิก"
                }
            },
            dismissible: false
        })
        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    this._service.void_order(id).subscribe({
                        complete: () => {
                            this.toastr.success('ดำเนินการ void สำเร็จ')
                            this.rerender()
                        },
                        error: () => {
                            this.toastr.error('เกิดข้อผิดพลาด')
                        }
                    })
                }
            }
        )
    }

    opendialogapro() {
        const DialogRef = this.dialog.open(DialogForm, {
            disableClose: true,
            width: '500px',
            height: 'auto',
            enterAnimationDuration: 300,
            exitAnimationDuration: 300,
            data: {
                type: 'NEW'
            }
        });
        DialogRef.afterClosed().subscribe((result) => {
            if (result) {
                console.log(result, 'result')
                this.rerender();
            }
        });
    }

    openDialogEdit(item: any) {
        this._service.getById(item.id).subscribe((resp: any) => {

            const DialogRef = this.dialog.open(DialogForm, {
                disableClose: false,
                width: '90vw', // 90% ของ viewport width
                maxWidth: '500px', // ไม่เกิน 500px
                maxHeight: '100vh',
                enterAnimationDuration: 300,
                exitAnimationDuration: 300,
                data: {
                    type: 'EDIT',
                    value: resp
                }
            });
            DialogRef.afterClosed().subscribe((result) => {
                if (result) {
                    console.log(result, 'result')
                    this.rerender();
                }
            });
        })

    }

    clickDelete(id: any) {
        const confirmation = this.fuseConfirmationService.open({
            title: "ยืนยันลบข้อมูล",
            message: "กรุณาตรวจสอบข้อมูล หากลบข้อมูลแล้วจะไม่สามารถนำกลับมาได้",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "warn"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "ยืนยัน",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "ยกเลิก"
                }
            },
            dismissible: false
        })
        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    this._service.delete(id).subscribe({
                        error: (err) => {

                        },
                        complete: () => {
                            this.toastr.success('ดำเนินการลบสำเร็จ');
                            this.rerender();
                        },
                    });
                }
            }
        )
    }
}


