<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
    <div class="flex flex-row justify-between pb-2 my-5">
      <div>
        <h2 class="text-2xl md:text-xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
          รายการยอดขาย
        </h2>
      </div>
    </div>
    <div class="flex flex-col md:flex-row md:items-end gap-4">
      <!-- วันที่ -->
      <mat-form-field appearance="fill" class="w-full md:w-64">
        <mat-label>วันที่</mat-label>
        <input matInput [matDatepicker]="picker" [(ngModel)]="datePicked" placeholder="วันที่"
          (dateChange)="filterdate()">
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
      </mat-form-field>

      <!-- สถานะ -->
      <mat-form-field class="w-full md:w-64">
        <mat-label>สถานะ</mat-label>
        <mat-select [(ngModel)]="filter_status" (selectionChange)="onChangeStatus()">
          <mat-option [value]="''">ALL</mat-option>
          <mat-option *ngFor="let item of filterStatus" [value]="item">
            {{item}}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <!-- อุปกรณ์ -->
      <mat-form-field class="w-full md:w-64">
        <mat-label>อุปกรณ์</mat-label>
        <mat-select [(ngModel)]="filter_device" (selectionChange)="onChangeDevice()">
          <mat-option [value]="''">ALL</mat-option>
          <mat-option *ngFor="let item of filterDevice" [value]="item.id">
            {{item.name}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div class="overflow-auto">
      <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="row-border hover w-full">
      </table>
    </div>
  </div>
</div>

<ng-template #btNg let-data="adtData">
  <button mat-icon-button [color]="'primary'" [matMenuTriggerFor]="menu">
    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cog-8-tooth'"></mat-icon>
  </button>
  <mat-menu #menu="matMenu">
    <button mat-menu-item (click)="openDialogEdit(data)">
      <mat-icon [svgIcon]="'heroicons_solid:eye'"></mat-icon>
      <span>ดูรายละเอียด</span>
    </button>
    <mat-divider></mat-divider>
    <button mat-menu-item (click)="void_order(data.id)">
      <mat-icon [svgIcon]="'heroicons_outline:receipt-refund'"></mat-icon>
      <span>void</span>
    </button>
    <!-- <button mat-menu-item (click)="clickDelete(data.id)">
      <mat-icon [svgIcon]="'heroicons_solid:trash'"></mat-icon>
      <span>ลบ</span>
    </button> -->
  </mat-menu>
</ng-template>
<ng-template #grandTotal let-data="adtData">
  <p>{{data.grandTotal | number : '1.2'}}</p>
</ng-template>