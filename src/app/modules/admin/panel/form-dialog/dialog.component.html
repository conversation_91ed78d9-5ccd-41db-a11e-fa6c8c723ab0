<div class="md:max-w-lg">
  <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'NEW'">เลือกสินค้า</h1>
  <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'EDIT'">แก้ไขข้อมูล</h1>
  <div mat-dialog-content class="overflow-y-auto md:max-h-180">
      <div class="flex-auto">
          <div class="flex flex-col md:flex mb-6">
              <div class="flex flex-col px-6 py-4">
                  <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                      <mat-select [placeholder]="'ระบุหมวดหมู่'" (selectionChange)="ChangeCategory($event)">
                          <mat-option *ngFor="let category of categories;"
                              [value]="category.id">{{category.name}}</mat-option>
                      </mat-select>
                  </mat-form-field>
                  <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                      <mat-select [placeholder]="'ระบุสินค้า'" (selectionChange)="selectProduct($event)">
                          <mat-option *ngFor="let prod of product;" [value]="prod">{{prod.name}}</mat-option>
                      </mat-select>
                  </mat-form-field>
              </div>
          </div>
      </div>
  </div>
  <!-- <div> check : {{this.form.value | json}}</div> -->
  <div mat-dialog-actions class="flex justify-end">
      <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
          ตกลง
      </button>
      <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
          ยกเลิก
      </button>
  </div>
</div>
