<div class="flex flex-col flex-auto min-w-0">
  <form [formGroup]="form">
    <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
      <div class="flex flex-col pb-2 my-5">
        <div class="flex justify-between mb-5">
          <h2 class="text-2xl md:text-xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
            สร้างหน้าจอ
          </h2>
          <button mat-flat-button [color]="'primary'" (click)="addRow()">
            <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:plus-circle'"></mat-icon>
            <span class="ml-2">เพิ่มหน้าจอ</span>
          </button>
        </div>
        <div mat-dialog-content class="overflow-y-auto md:max-h-180">
          <div class="flex-auto">
            <div class="flex flex-col md:flex mb-6">
              <div class=" md:w-full">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-1/4">
                  <mat-label>ชื่อหน้าจอ</mat-label>
                  <input matInput [placeholder]="'กรุณาระบุรหัสสาขา'" formControlName="name">
                </mat-form-field>
              </div>
            </div>
          </div>
        </div>

      </div>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4" formArrayName="products">
        <ng-container *ngFor="let item of products.controls; let i = index" [formGroupName]="i">
          <div class="max-w-sm overflow-hidden shadow-lg rounded-2xl">
            <!-- <img class="w-full" src="assets/images/no_image.jpg" alt="Sunset in the mountains"> -->
            <div class="flex flex-col px-6 py-4">
              <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                <mat-label>หมวดหมู่สินค้า</mat-label>
                  <input matInput [placeholder]="''" formControlName="category_name" readonly>
              </mat-form-field>
              <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                <mat-label>สินค้า</mat-label>
                <input matInput [placeholder]="'กรุณาระบุรหัสสาขา'" formControlName="product_name" readonly>
              </mat-form-field>
            </div>
            <div class="px-6 pt-4 pb-4 w-full flex justify-between items-center gap-2">
              <button mat-flat-button [color]="'primary'" (click)="opendialogAdd(item)">
                <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:plus-circle'"></mat-icon>
                <span class="ml-2">เลือกสินค้า</span>
              </button>
              <button mat-flat-button [color]="'warn'" (click)="removeRow(i)">
                <mat-icon class="icon-size-5" [svgIcon]="'heroicons_outline:trash'"></mat-icon>
                <span class="ml-2">ลบ</span>
              </button>
            </div>
          </div>
        </ng-container>
      </div>
      <div mat-dialog-actions class="flex justify-end">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
            ตกลง
        </button>
        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="backTo()">
            ยกเลิก
        </button>
      </div>
    </div>
    <!-- <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
      <div class="flex flex-row justify-between pb-2">
        <div>
          <h2 class="text-2xl md:text-xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
            หน้าจอ
          </h2>

        </div>

      </div>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4" formArrayName="products">
        <ng-container *ngFor="let item of products.controls; let i = index" [formGroupName]="i">
          <div class="max-w-sm overflow-hidden shadow-lg rounded-2xl">
            <img class="w-full" src="assets/images/no_image.jpg" alt="Sunset in the mountains">
            <div class="flex flex-col px-6 py-4">
              <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                <mat-label>หมวดหมู่สินค้า</mat-label>
                  <input matInput [placeholder]="''" formControlName="category_name" readonly>
              </mat-form-field>
              <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                <mat-label>สินค้า</mat-label>
                <input matInput [placeholder]="'กรุณาระบุรหัสสาขา'" formControlName="product_name" readonly>
              </mat-form-field>
            </div>
            <div class="px-6 pt-4 pb-4 w-full flex justify-between items-center">
              <button mat-flat-button [color]="'primary'" (click)="opendialogAdd(item)">
                <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:plus-circle'"></mat-icon>
                <span class="ml-2">เลือกสินค้า</span>
              </button>
              <button mat-flat-button [color]="'warn'" (click)="removeRow(i)">
                <mat-icon class="icon-size-5" [svgIcon]="'heroicons_outline:trash'"></mat-icon>
                <span class="ml-2">ลบ</span>
              </button>
            </div>
          </div>
        </ng-container>
      </div>
      <div mat-dialog-actions class="flex justify-end">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
            ตกลง
        </button>
        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="backTo()">
            ยกเลิก
        </button>
      </div>
    </div> -->
  </form>
</div>
