<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
    <div class="flex flex-row justify-between pb-2 my-5">
      <div>
        <h2 class="text-2xl md:text-xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
          รายการแตะบัตร Buffet
        </h2>
      </div>
      <button mat-flat-button [color]="'primary'" (click)="opendialogAdd()">
        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:plus-circle'"></mat-icon>
        <span class="ml-2"> เพิ่มแตะบัตร</span>
      </button>
    </div>
    <div class="flex items-center justify-start gap-4">
      <mat-form-field appearance="fill">
        <mat-label>วันที่</mat-label>
        <input matInput [matDatepicker]="picker" [(ngModel)]="datePicked" placeholder="วันที่" (dateChange)="filterdate()">
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
      </mat-form-field>
      <mat-form-field class="w-full md:w-1/4">
        <mat-label>ผู้ใช้งาน</mat-label>
        <mat-select [(ngModel)]="selected_foodType" (selectionChange)="onChangeFoodType()">
            <mat-option  [value]="''" >
                ALL
            </mat-option>
            <mat-option *ngFor="let item of foodType" [value]="item.id" >
                {{item.name}}
            </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="table w-full bg-white row-border hover whitespace-nowrap">
    </table>
  </div>
</div>
<ng-template #btNg let-data="adtData">
  <button mat-icon-button [color]="'primary'" [matMenuTriggerFor]="menu">
    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cog-8-tooth'"></mat-icon>
  </button>
  <mat-menu #menu="matMenu">
    <button mat-menu-item (click)="void_buffet(data.id)">
      <mat-icon [svgIcon]="'heroicons_outline:receipt-refund'"></mat-icon>
      <span>void</span>
    </button>
    <!--<mat-divider></mat-divider>
    <button mat-menu-item (click)="clickDelete(data.id)">
      <mat-icon [svgIcon]="'heroicons_solid:trash'"></mat-icon>
      <span>ลบ</span>
    </button>-->
  </mat-menu>
</ng-template>
