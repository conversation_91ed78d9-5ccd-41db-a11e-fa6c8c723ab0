<div class="md:max-w-lg" >
    <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'NEW'">เพิ่มข้อมูลแตะบัตร</h1>
    <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'EDIT'">แก้ไขข้อมูล</h1>
    <div mat-dialog-content class="overflow-y-auto md:max-h-180">
        <form [formGroup]="form">
            <div class="flex-auto">
                <div class="flex flex-col md:flex mb-6">
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" >
                            <mat-label>รหัสพนักงาน</mat-label>
                            <input matInput [placeholder]="'กรุณาระบุรหัสพนักงาน'" formControlName="code">
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" >
                            <mat-label>ประเภทอาหาร</mat-label>
                            <mat-select formControlName="foodType" [placeholder]="'กรุณาเลือกประเภทอาหาร'">
                                <mat-option *ngFor="let item of foodType;" [value]="item.key">
                                    {{item.value}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" >
                            <mat-label>รหัสอุปกรณ์</mat-label>
                            <mat-select formControlName="deviceId" [placeholder]="'กรุณาเลือกอุปกรณ์'">
                                <mat-option *ngFor="let item of devices;" [value]="item?.id">
                                    {{item?.name}}
                                </mat-option>
                            </mat-select>
                          </mat-form-field>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- <div> check : {{this.form.value | json}}</div> -->
    <div mat-dialog-actions class="flex justify-end">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
            ตกลง
        </button>
        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
            ยกเลิก
        </button>
    </div>
</div>