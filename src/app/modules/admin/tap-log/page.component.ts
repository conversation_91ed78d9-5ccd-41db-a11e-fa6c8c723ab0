import { CommonModule, DatePipe, NgClass } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { TaplogService } from './page.service';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { FilePickerModule } from 'ngx-awesome-uploader';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { DialogRef } from '@angular/cdk/dialog';
import { DialogForm } from './form-dialog/dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { DateTime } from 'luxon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDatepicker, MatDatepickerModule } from '@angular/material/datepicker';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
@Component({
    selector: 'app-page-tap-log',
    standalone: true,
    providers: [DatePipe],
    imports: [
        CommonModule,
        DataTablesModule,
        MatButtonModule,
        MatIconModule,
        FilePickerModule,
        MatMenuModule,
        MatDividerModule,
        MatFormFieldModule,
        FormsModule,
        MatDatepickerModule,
        NgClass,
        ReactiveFormsModule,
        MatSelectModule,
        MatInputModule
    ],
    templateUrl: './page.component.html',
    styleUrl: './page.component.scss',
    changeDetection: ChangeDetectionStrategy.Default,
})
export class TapLogComponent implements OnInit, AfterViewInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    @ViewChild('btNg') btNg: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;

    @Input() storeId: any;
    @Output() dataArrayChange = new EventEmitter<any[]>();
    data_day: any;
    data_month: any;
    data_year: any;
    datePicked: any;
    selected_foodType: any;

    foodType: any[] = [
        { id: 'Asian', name: 'Asian' },
        { id: 'Western', name: 'Western' },
        { id: 'Noodle', name: 'Noodle' },
    ]

    constructor(
        private _service: TaplogService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        public dialog: MatDialog,
        private datePipe: DatePipe
    )
    { }

    ngOnInit(): void {
        setTimeout(() =>
            this.loadTable());
    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }

    onChangeFoodType(){
        this.rerender()
    }

    loadTable(): void {
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true, // Set the flag
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.filter = {
                    'filter.timestamp': (this.data_day && this.data_month && this.data_year) ? `$btw:${this.data_year}-${this.data_month}-${this.data_day} 00:00:00,${this.data_year}-${this.data_month}-${this.data_day} 23:59:59` : '' ,
                    'filter.foodType': this.selected_foodType ?? '',
                },
                this._service.datatable(dataTablesParameters).subscribe({
                    next: (resp: any) => {
                        callback({
                            recordsTotal: resp.meta.totalItems,
                            recordsFiltered: resp.meta.totalItems,
                            data: resp.data
                        });
                    },error: () => {
                        this.toastr.error('เกิดข้อผิดพลาด')
                    }
                })
            },
            columns: [
                {
                    title: 'ลำดับ',
                    data: 'no',
                    className: 'w-15 text-center h-[40px]'
                },
                {
                    title: 'วันที่ทำรายการ',
                    defaultContent: '-',
                    data: function(row: any) {
                        return DateTime.fromISO(row.timestamp).toFormat('dd/MM/yyyy HH:mm:ss');
                    },
                    className: 'text-center'
                },
                {
                    title: 'รหัส',
                    data: 'member.code',
                    className: 'w-15 text-center'
                },
                {
                    title: 'ชื่อ',
                    data: 'memberFirstname',
                    className: 'w-20 text-center'
                },
                {
                    title: 'ชื่อกลาง',
                    data: 'memberMiddlename',
                    className: 'w-20 text-center'
                },
                {
                    title: 'นามสกุล',
                    data: 'memberLastname',
                    className: 'w-20 text-center'
                },
                {
                    title: 'ประเภทอาหาร',
                    data: 'foodType',
                    className: 'text-center'
                },
                {
                    title: 'ราคา',
                    data: 'price',
                    className: 'text-center'
                },
                {
                    title: 'ยอดเงินคงเหลือ',
                    data: 'balance',
                    className: 'text-center'
                },
                {
                    title: 'สถานะ',
                    data: 'status',
                    className: 'text-center'
                },
                {
                    title: 'จัดการ',
                    data: null,
                    defaultContent: '',
                    ngTemplateRef: {
                        ref: this.btNg,
                    },
                    className: 'w-15 text-center'
                }
            ]
        }
    }

    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(this.dtOptions);
        });
    }

    void_buffet(id: any){
        const confirmation = this.fuseConfirmationService.open({
            title: "คุณต้องการ void ใช่หรือไม่ ?",
            message: "กรุณาตรวจสอบให้แน่ใจ หากยืนยันแล้วจะไม่สามารถย้อนกลับ",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "warn"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "ยืนยัน",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "ยกเลิก"
                }
            },
            dismissible: false
        })
        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    this._service.void_buffet(id).subscribe({
                        complete:()=>{
                            this.toastr.success('ดำเนินการ void สำเร็จ')
                            this.rerender()
                        },
                        error:()=>{
                            this.toastr.error('เกิดข้อผิดพลาด')
                        }
                    })
                }
            }
        )
    }

    opendialogAdd() {
        const DialogRef = this.dialog.open(DialogForm, {
            disableClose: true,
            width: '500px',
            height: 'auto',
            enterAnimationDuration: 500,
            exitAnimationDuration: 300,
            data: {
                type: 'NEW',
                value: '',
                store: this.storeId
            }
        });
        DialogRef.afterClosed().subscribe((result) => {
            if (result) {
                console.log(result, 'result')
                this.rerender();
            }
        });
    }

    branch: any
    openDialogEdit(item: any) {
        this._service.getBranchId(item).subscribe( (resp: any)=>{
            this.branch = resp;
            const DialogRef = this.dialog.open(DialogForm, {
                disableClose: true,
                width: '500px',
                height: 'auto',
                enterAnimationDuration: 500,
                exitAnimationDuration: 300,
                data: {
                    type: 'EDIT',
                    value: this.branch,
                    store: this.storeId
                }
            });
            DialogRef.afterClosed().subscribe((result) => {
                if (result) {
                    console.log(result, 'result')
                    this.rerender();
                }
            });
        })

    }

    clickDelete(id: any) {
        const confirmation = this.fuseConfirmationService.open({
            title: "ยืนยันลบข้อมูล",
            message: "กรุณาตรวจสอบข้อมูล หากลบข้อมูลแล้วจะไม่สามารถนำกลับมาได้",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "warn"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "ยืนยัน",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "ยกเลิก"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    this._service.delete(id).subscribe({
                        error: (err) => {

                        },
                        complete: () => {
                            this.toastr.success('ดำเนินการลบสำเร็จ');
                            this.rerender();
                        },
                    });
                }
            }
        )
    }

    filterdate(){
        console.log("datePicked: ",this.datePicked);
        this.data_day = this.datePicked ? this.datePipe.transform(this.datePicked, 'dd') : 0;
        this.data_month = this.datePicked ? this.datePipe.transform(this.datePicked, 'MM') : 12;
        this.data_year = this.datePicked ? this.datePipe.transform(this.datePicked, 'yyyy') : 2024;
        console.log(`${this.data_day}, ${this.data_month}, ${this.data_year}`);
        this.rerender()
    }
}
