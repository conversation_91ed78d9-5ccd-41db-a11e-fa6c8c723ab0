<div class="flex flex-col overflow-y-auto">
  <!--<mat-toolbar class="flex justify-between">
      <span class="example-spacer"></span>-->

      <div>
        <h1 mat-dialog-title class="mb-4 text-xl font-semibold" *ngIf="this.data?.type === 'NEW'">เพิ่มข้อมูล</h1>
        <h1 mat-dialog-title class="mb-4 text-xl font-semibold" *ngIf="this.data?.type === 'EDIT'">แก้ไขข้อมูล</h1>
      </div>

      <!--<button mat-icon-button mat-dialog-close class="example-icon " aria-label="Example icon-button with menu icon">
          <mat-icon svgIcon="heroicons_outline:x-mark"></mat-icon>
      </button>
  </mat-toolbar>-->
  <div class="flex flex-col justify-between">
      <div class="flex justify-center">
          <form class="flex flex-col w-[712px] h-fit p-8 pb-4 rounded-2xl overflow-hidden "
              [formGroup]="form">
              <div class="grid w-full gap-3 ">
                    <div class="w-full mb-4">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>ชื่อแบนเนอร์</mat-label>
                            <input matInput [formControlName]="'title'" required [placeholder]="'ระบุชื่อแบนเนอร์'">
                            <mat-error>กรุณาระบุชื่อแบนเนอร์</mat-error>
                        </mat-form-field>
                    </div>
                    <div class="w-full mb-4">
                        <mat-slide-toggle [formControlName]="'isShow'" [color]="'primary'">แสดง</mat-slide-toggle>
                    </div>
              </div>


              <div class="flex justify-center">
                  <ngx-awesome-uploader [adapter]="adapter" [accept]="'image/*'" [uploadType]="'single'"
                      class="w-full" [fileMaxSize]="200" (uploadSuccess)="uploadSuccess($event)"
                      (validationError)="onValidationError($event)">

                  </ngx-awesome-uploader>

                  <!-- <asha-image-upload [initial]="item?.imageUrl" (uploadSuccess)="uploadSuccess($event)"></asha-image-upload> -->
              </div>
          </form>
      </div>
      <div class="bottom-0 flex justify-between w-full pt-5 mt-8 bg-white border-t dark:bg-gray-700 ">
          <div>
              <!-- <button mat-stroked-button color="primary"
              class="gap-2 border-2 border-red-500 text-primary-600 rounded-xl">ลบลูกค้า
              <mat-icon class="text-primary-500" svgIcon="heroicons_outline:trash"></mat-icon>
          </button> -->
          </div>
          <div mat-dialog-actions class="flex justify-end">
              <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
                  ตกลง
              </button>
              <button class="px-6 ml-3" mat-flat-button [color]="'warn'" mat-dialog-close>
                  ยกเลิก
              </button>
          </div>
      </div>
  </div>
</div>
