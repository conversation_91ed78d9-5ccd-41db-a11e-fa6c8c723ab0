<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto p-4 m-4 rounded-md sm:p-10 bg-card">
    <div class="flex flex-row justify-between pb-2 my-5">
      <div>
        <h2 class="text-2xl font-extrabold leading-7 tracking-tight truncate md:text-xl sm:leading-10">
          รายการสินค้า
        </h2>
      </div>
      <div class="ml-auto flex space-x-4">
        <button mat-flat-button [color]="'primary'" (click)="opendialogapro2()">
          <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:plus-circle'"></mat-icon>
          <span class="ml-2"> นำเข้าข้อมูล</span>
        </button>
        <button mat-flat-button [color]="'primary'" (click)="createProduct()">
          <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:plus-circle'"></mat-icon>
          <span class="ml-2"> เพิ่มสินค้า</span>
        </button>
      </div>

    </div>
    <form [formGroup]="form">
      <div class="md:w-1/4 w-full">
        <mat-form-field class="w-full">
          <mat-label>เลือกประเภทสินค้า</mat-label>
          <mat-select [formControlName]="'category_id'" (selectionChange)="onChangeType()">
            <mat-option [value]="">
              ทั้งหมด
            </mat-option>
            <mat-option *ngFor="let item of catagories;" [value]="item.id">
              {{item.name}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </form>
    <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="row-border hover w-full">
    </table>
  </div>
</div>

<ng-template #btNg let-data="adtData">
  <button mat-icon-button [color]="'primary'" [matMenuTriggerFor]="menu">
    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cog-8-tooth'"></mat-icon>
  </button>

  <mat-menu #menu="matMenu">
    <button mat-menu-item (click)="openDialogEdit(data)">
      <mat-icon [svgIcon]="'heroicons_solid:pencil-square'"></mat-icon>
      <span>แก้ไข</span>
    </button>
    <mat-divider></mat-divider>
    <button mat-menu-item (click)="clickDelete(data.id)">
      <mat-icon [svgIcon]="'heroicons_solid:trash'"></mat-icon>
      <span>ลบ</span>
    </button>
  </mat-menu>
</ng-template>

<ng-template #btPicture let-data="adtData">
  <button *ngIf="data.imageUrl" mat-icon-button [color]="'primary'" (click)="showPicture(data.imageUrl)">
    <mat-icon class="icon-size-5" [svgIcon]="'mat_solid:image'"></mat-icon>
  </button>
</ng-template>

<ng-template #activeStatus let-data="adtData">
  <app-status-badge [active]="data.active"></app-status-badge>
</ng-template>

<ng-template #vatStatus let-data="adtData">
  <div *ngIf="data.vatStatus; else inactiveTemplate">
    <span class="bg-green-300 rounded-xl py-2 px-6 font-semibold">true</span>
  </div>
  <ng-template #inactiveTemplate>
    <div>
      <span class="bg-red-300 rounded-xl py-2 px-6 font-semibold">false</span>
    </div>
  </ng-template>
</ng-template>
<ng-template #remarkStatus let-data="adtData">
  <div *ngIf="data.remarkStatus; else inactiveTemplate">
    <span class="bg-green-300 rounded-xl py-2 px-6 font-semibold">true</span>
  </div>
  <ng-template #inactiveTemplate>
    <div>
      <span class="bg-red-300 rounded-xl py-2 px-6 font-semibold">false</span>
    </div>
  </ng-template>
</ng-template>