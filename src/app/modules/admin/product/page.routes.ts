import { Routes } from '@angular/router';
import { ProductComponent } from './page.component';
import { inject } from '@angular/core';
import { ProductService } from './product.service';
import { FormProductComponent } from './form/form.component';


export default [
  {
    path: '',
    component: ProductComponent,
    resolve: {
      categories: () => inject(ProductService).getCategories(),
      units: () => inject(ProductService).getUnit(),
      branch: () => inject(ProductService).getฺBranch(),
      // products  : () => inject(InventoryService).getProducts(),
    },
  },

  {
    path: 'form',
    component: FormProductComponent,
    resolve: {
      categories: () => inject(ProductService).getCategories(),
      units: () => inject(ProductService).getUnit(),
      vendors: () => inject(ProductService).getVendors(),
      branch: () => inject(ProductService).getฺBranch(),
      // products  : () => inject(InventoryService).getProducts(),
    },
  },
  {
    path: 'edit/:id',
    component: FormProductComponent,
    resolve: {
      categories: () => inject(ProductService).getCategories(),
      units: () => inject(ProductService).getUnit(),
      vendors: () => inject(ProductService).getVendors(),
      branch: () => inject(ProductService).getฺBranch(),
      // products  : () => inject(InventoryService).getProducts(),
    },
  }

] as Routes;
