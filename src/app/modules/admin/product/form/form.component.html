<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto p-6 m-4 rounded-lg shadow-sm bg-white">
    <div class="flex flex-row justify-between pb-4 mb-6 border-b border-gray-100">
      <div>
        <h2 class="text-2xl font-semibold text-gray-800">
          รายการสินค้า
        </h2>
        <p class="text-sm text-gray-500">จัดการข้อมูลสินค้าของคุณ</p>
      </div>
    </div>

    <form [formGroup]="productForm" class="grid gap-6 md:grid-cols-2 p-6 bg-gray-50 rounded-lg">
      <!-- Row 1 -->
      <mat-form-field appearance="outline" class="bg-white rounded">
        <mat-label>ชื่อสินค้า</mat-label>
        <input matInput formControlName="name" />
        <mat-icon matSuffix>inventory_2</mat-icon>
      </mat-form-field>

      <mat-form-field appearance="outline" class="bg-white rounded">
        <mat-label>รหัสสินค้า</mat-label>
        <input matInput formControlName="code" />
        <mat-icon matSuffix>tag</mat-icon>
      </mat-form-field>

      <!-- Row 2 -->
      <mat-form-field appearance="outline" class="bg-white rounded col-span-2">
        <mat-label>บาร์โค้ด</mat-label>
        <input matInput formControlName="barcode" />
        <mat-icon matSuffix>barcode</mat-icon>
      </mat-form-field>
      <!-- Row 3 -->
      <mat-form-field appearance="outline" class="bg-white rounded">
        <mat-label>ราคา</mat-label>
        <input matInput type="number" formControlName="price" />
      </mat-form-field>

      <mat-form-field appearance="outline" class="bg-white rounded">
        <mat-label>ต้นทุน</mat-label>
        <input matInput type="number" formControlName="cost" />
      </mat-form-field>

      <!-- Row 4 -->
      <mat-form-field appearance="outline" class="bg-white rounded">
        <mat-label>ประเภทแสดงผล</mat-label>
        <mat-select formControlName="showType">
          <mat-option value="color">สี</mat-option>
          <mat-option value="image">รูปภาพ</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline" class="bg-white rounded">
        <mat-label>หมวดหมู่</mat-label>
        <mat-select formControlName="categoryId">
          <mat-option *ngFor="let category of catagories" [value]="category.id">
            {{category.name}}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <!-- Row 5 -->
      <mat-form-field appearance="outline" class="bg-white rounded">
        <mat-label>หน่วยนับ</mat-label>
        <mat-select formControlName="unitId">
          <mat-option *ngFor="let unit of units" [value]="unit.id">
            {{unit.name}}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <div class="flex items-center gap-4 bg-white p-4 rounded w-full">
        <mat-checkbox class="w-full" formControlName="active" color="primary">เปิดใช้งาน</mat-checkbox>

        <div class="flex items-center gap-2 w-full">
          <label class="text-sm font-medium text-gray-700">สี:</label>
          <input type="color" formControlName="color" class="w-8 h-8 border rounded cursor-pointer" />
        </div>
      </div>

      <!-- Remarks -->
      <mat-form-field appearance="outline" class="md:col-span-2 bg-white rounded">
        <mat-label>หมายเหตุ</mat-label>
        <textarea matInput formControlName="remark" rows="3"></textarea>
      </mat-form-field>

      <!-- Vendor ID List -->
      <div class="md:col-span-2 bg-white p-4 rounded-lg shadow-xs">
        <label class="block mb-3 text-sm font-medium text-gray-700">ผู้จำหน่าย</label>
        <div formArrayName="vendorId" class="flex flex-col gap-3">
          <div *ngFor="let ctrl of productForm.get('vendorId')['controls']; let i = index"
            class="flex items-center gap-3">
            <mat-form-field appearance="outline" class="flex-1">
              <mat-label>ผู้จำหน่าย {{i + 1}}</mat-label>
              <mat-select [formControlName]="i">
                <mat-option *ngFor="let vendor of vendors" [value]="vendor.id">
                  {{vendor.name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
            <button mat-icon-button color="warn" class="hover:bg-red-50" (click)="removeVendor(i)">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
          <button mat-stroked-button color="primary" class="self-start mt-2" (click)="addVendor()">
            <mat-icon>add</mat-icon>
            เพิ่มผู้จำหน่าย
          </button>
        </div>
      </div>

      <!-- Branch ID List -->
      <div class="md:col-span-2 bg-white p-4 rounded-lg shadow-xs">
        <label class="block mb-3 text-sm font-medium text-gray-700">สาขาที่ใช้</label>
        <div formArrayName="branchIds" class="flex flex-col gap-3">
          <div *ngFor="let ctrl of productForm.get('branchIds')['controls']; let i = index"
            class="flex items-center gap-3">
            <mat-form-field appearance="outline" class="flex-1">
              <mat-label>สาขา {{i + 1}}</mat-label>
              <mat-select [formControlName]="i">
                <mat-option *ngFor="let branch of branches" [value]="branch.id">
                  {{branch.name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
            <button mat-icon-button color="warn" class="hover:bg-red-50" (click)="removeBranch(i)">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
          <button mat-stroked-button color="primary" class="self-start mt-2" (click)="addBranch()">
            <mat-icon>add</mat-icon>
            เพิ่มสาขา
          </button>
        </div>
      </div>
      <div class="md:col-span-2 bg-white p-4 rounded-lg shadow-xs">
        <label class="block mb-3 text-sm font-medium text-gray-700">สาขาที่ใช้</label>
        <asha-image-upload [initial]="item?.imageUrl" (uploadSuccess)="uploadSuccess($event)"></asha-image-upload>
      </div>

      <!-- Submit -->
      <div class="md:col-span-2 flex justify-end gap-3 pt-4 border-t border-gray-100">
        <button mat-stroked-button color="basic" type="button" (click)="backTo()">
          ยกเลิก
        </button>
        <button mat-flat-button color="primary" class="px-6" (click)="onSubmit()">
          <mat-icon>save</mat-icon>
          บันทึกข้อมูล
        </button>
      </div>
    </form>
  </div>
</div>