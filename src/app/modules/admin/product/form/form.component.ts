import { CommonModule, Cur<PERSON>cyPipe, DecimalPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, OnInit, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';

import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { FilePickerModule } from 'ngx-awesome-uploader';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';

import { FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { ProductService } from '../product.service';
import { StatusBadgeComponent } from 'app/modules/shared/status-badge/status-badge.component';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { ImageUploadService } from 'app/modules/common/image-upload/image-upload.service';
import { ImageUploadComponent } from 'app/modules/common/image-upload/image-upload.component';
@Component({
    selector: 'app-form-product',
    standalone: true,
    providers: [
        CurrencyPipe,
        DecimalPipe
    ],
    imports: [
        CommonModule,
        MatButtonModule,
        MatIconModule,
        FilePickerModule,
        MatMenuModule,
        MatDividerModule,
        ReactiveFormsModule,
        FormsModule,
        MatSelectModule,
        MatInputModule,
        MatFormFieldModule,
        MatCheckboxModule,
        ImageUploadComponent

    ],
    templateUrl: './form.component.html',
    styleUrl: './form.component.scss',
    changeDetection: ChangeDetectionStrategy.Default,
})
export class FormProductComponent implements OnInit, AfterViewInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    catagories: any[] = [];
    categories: any[] = [];
    units: any[] = [];
    vendors: any[] = [];
    branches: any[] = [];
    item: any
    form: FormGroup;
    productForm: FormGroup
    Id: any
    constructor(
        private _service: ProductService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        public dialog: MatDialog,
        private _router: Router,
        private fb: FormBuilder,
        private currencyPipe: CurrencyPipe,
        private decimalPipe: DecimalPipe,
        private imageUploadService: ImageUploadService,
        private _activatedRoute: ActivatedRoute
    ) {
        this.Id = this._activatedRoute.snapshot.params.id
        this._service.categories$.subscribe(resp => this.catagories = resp);
        this._service.units$.subscribe(resp => this.units = resp);
        this._service.vendors$.subscribe(resp => this.vendors = resp);
        this._service.branch$.subscribe(resp => this.branches = resp);
        this.productForm = this.fb.group({
            color: ['#00ff00'],
            barcode: [''],
            code: [''],
            name: [''],
            price: [0, [Validators.required, Validators.min(0)]],
            cost: [0, [Validators.required, Validators.min(0)]],
            showType: ['color'],
            image: [''],
            categoryId: [null, Validators.required],
            unitId: [null, Validators.required],
            active: [true],
            remark: [''],
            vendorId: this.fb.array([this.fb.control(null)]), // Array of vendor IDs
            branchIds: this.fb.array([this.fb.control(null)]) // Array of branch IDs
        });

    }
    ngOnInit(): void {
        if (this.Id) {

            this._service.getById(this.Id).subscribe((resp) => {
                console.log(resp);

                const data = resp
                this.item = resp
                this.productForm.patchValue({
                    color: data.color,
                    barcode: data.barcode,
                    code: data.code,
                    name: data.name,
                    price: data.price,
                    cost: data.cost,
                    showType: data.showType,
                    image: data.image,
                    categoryId: data.category?.id ?? null,
                    unitId: data.unit?.id ?? null,
                    active: data.active,
                    remark: data.remark
                });
                const vendorIds = data.vendors?.map(v => v.id) ?? [];
                const branchIds = data.branches?.map(b => b.id) ?? [];

                this.productForm.setControl(
                    'vendorId',
                    this.fb.array(vendorIds.map(id => this.fb.control(id)))
                );

                this.productForm.setControl(
                    'branchIds',
                    this.fb.array(branchIds.map(id => this.fb.control(id)))
                );
            })
        }
    }

    ngAfterViewInit() {


    }

    onChangeType() {
    }


    uploadSuccess(event): void {
        this.imageUploadService.upload(event).subscribe({
            next: (resp: any) => {
                this.productForm.patchValue({
                    image: resp.filename,
                    showType: 'image'
                }); 
                console.log(this.productForm.value);

            },
            error: (err) => {
                alert(JSON.stringify(err))
            },

        })
    }


    // Convenience getter
    get vendorId(): FormArray {
        return this.productForm.get('vendorId') as FormArray;
    }

    get branchIds(): FormArray {
        return this.productForm.get('branchIds') as FormArray;
    }

    addVendor(): void {
        this.vendorId.push(this.fb.control(0));
    }

    removeVendor(index: number): void {
        if (this.vendorId.length > 1) {
            this.vendorId.removeAt(index);
        }
    }

    addBranch(): void {
        this.branchIds.push(this.fb.control(0));
    }

    removeBranch(index: number): void {
        if (this.branchIds.length > 1) {
            this.branchIds.removeAt(index);
        }
    }

    onSubmit() {
        const confirmation = this.fuseConfirmationService.open({
            title: "ยืนยันการบันทึกข้อมูล",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "primary"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "ยืนยัน",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "ยกเลิก"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    if (this.Id) {
                        this._service.update(this.Id,this.productForm.value).subscribe({
                            error: (err) => {
                                this.toastr.error(err.error.message)
                            },
                            complete: () => {
                                this.toastr.success('ดำเนินการแก้ไขข้อมูลสำเร็จ')
                                this._router.navigate(['product'])
                            },
                        });
                    } else {
                        this._service.create(this.productForm.value).subscribe({
                            error: (err) => {
                                this.toastr.error(err.error.message)
                            },
                            complete: () => {
                                this.toastr.success('ดำเนินการเพิ่มข้อมูลสำเร็จ')
                                this._router.navigate(['product'])
                            },
                        });
                    }
                }
            }
        )
    }

    backTo() {
        this._router.navigate(['product'])
    }
}
