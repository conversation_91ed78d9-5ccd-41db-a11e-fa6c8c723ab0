import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataTablesModule } from 'angular-datatables';
import { MatIconModule } from '@angular/material/icon';
import { MAT_FORM_FIELD_DEFAULT_OPTIONS, MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatDialog, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose, MatDialogRef, MAT_DIALOG_DATA, } from '@angular/material/dialog';
import { FormBuilder, FormGroup, FormsModule, Validators, FormArray, ValidatorFn, AbstractControl } from '@angular/forms';
import { MatToolbarModule } from '@angular/material/toolbar';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { HttpClient } from '@angular/common/http';
import { FilePickerModule } from 'ngx-awesome-uploader';
import { DemoFilePickerAdapter } from 'app/demo-file-picker.adapter';
import { NgxMaskDirective } from 'ngx-mask';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { ProductService } from '../../product.service';
import { ImageUploadComponent } from 'app/modules/common/image-upload/image-upload.component';
import { ImageUploadService } from 'app/modules/common/image-upload/image-upload.service';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';

@Component({
    selector: 'app-product-compose',
    standalone: true,
    templateUrl: './product-compose.component.html',
    styleUrl: './product-compose.component.scss',
    imports: [CommonModule, DataTablesModule, MatIconModule, MatFormFieldModule, MatInputModule,
        FormsModule, MatToolbarModule, MatButtonModule, MatDialogTitle, MatDialogContent, MatDialogActions,
        MatDialogClose, MatSelectModule, FilePickerModule, NgxMaskDirective, ReactiveFormsModule, MatTabsModule,
        MatDividerModule, ImageUploadComponent, MatSlideToggleModule
    ],
    providers: [
        { provide: MAT_FORM_FIELD_DEFAULT_OPTIONS, useValue: { appearance: 'outline' } }
    ]
})
export class ProductComposeComponent implements OnInit {

    selectedValue: string;
    form: FormGroup;
    catagories: any[] = [];
    units: any[] = [];
    title: string
    attForm: FormGroup;
    branch: any[] = [];
    selectedBranches: any[] = [];

    item: any

    constructor(
        @Inject(MAT_DIALOG_DATA) public data: { type: string, value: any },
        private dialogRef: MatDialogRef<ProductComposeComponent>,
        public dialog: MatDialog,
        private fb: FormBuilder,
        public productService: ProductService,
        private imageUploadService: ImageUploadService,
        private toastr: ToastrService,
        private fuseConfirmationService: FuseConfirmationService,
    ) { }

    ngOnInit(): void {

        this.form = this.fb.group({
            barcode: '',
            //code: ['', Validators.required],
            name: ['', Validators.required],
            type: ['', Validators.required],
            price: ['', Validators.required],
            cost: ['', Validators.required],
            image: [''],
            categoryId: ['', Validators.required],
            unit: ['', Validators.required],
            branchId: [[''], Validators.required],
            active: [true, Validators.required],
            remarkStatus: [false, Validators.required],
            vatStatus: [false, [this.validateNotEmptyArray(), Validators.required]],
        });

        this.attForm = this.fb.group({
            attributes: this.fb.array([])
        });

        if (this.data.type === 'NEW') {
            this.title = "เพิ่มสินค้า"

            //this.addAttribute()

        } else if (this.data.type === 'EDIT') {
            this.title = "แก้ไขสินค้า"

            this.item = this.data.value
            console.log(this.item);

            let temp_branchId = []
            for(let item of this.data?.value?.branches){
                temp_branchId.push(item?.id)
            }
            if(temp_branchId.length == 0){
                temp_branchId = ['']
            }

            this.form.patchValue({
                ...this.data.value,
                categoryId: this.data?.value?.category?.id,
                unit: this.data?.value?.unit?.id,
                branchId: temp_branchId,
                type: this.data?.value?.productType
            });
            console.log('this.form.value: ',this.form.value);
            

            for (const productAttribute of this.data.value.productAttributes) {
                this.addAttribute(productAttribute)
            }
        }

        this.productService.categories$.subscribe(resp => this.catagories = resp);
        this.productService.units$.subscribe(resp => this.units = resp);
        this.productService.branch$.subscribe(resp => this.branch = resp);
       

    }

    attributes(): FormArray {
        return this.attForm.get('attributes') as FormArray
    }

    attributeValues(index: number): FormArray {
        return this.attributes().at(index).get('attributeValues') as FormArray
    }

    addAttribute(data?: any) {
        const g = this.fb.group({
            id: [null],
            name: ['', Validators.required],
            type: ['', Validators.required],
            attributeValues: this.fb.array([])
        })

        if (data) {
            g.patchValue({
                ...data,
            });

            for (const productAttributeValue of data?.productAttributeValues) {
                this.addAttValue(g, productAttributeValue)
            }
        }

        this.attributes().push(g)
    }

    addAttValue(fg: any, data?: any) {
        const g = this.fb.group({
            id: [null],
            name: ['', Validators.required],
            price: ['', Validators.required],
        })

        if (data) {
            g.patchValue({
                ...data
            })
        }

        const attributeValues = fg.get('attributeValues') as FormArray

        attributeValues.push(g)
    }

    removeAttribute(index: number) {
        this.attributes().removeAt(index)
    }

    removeAttValue(i: number, j: number) {
        this.attributeValues(i).removeAt(j)
    }

    Submit() {
        if (this.data.type === 'NEW') {
            this.create()
        } else {
            this.update()
        }
    }

    create() {
        let formValue = this.form.value
        const confirmation = this.fuseConfirmationService.open({
            title: "ยืนยันการบันทึกข้อมูล",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "primary"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "ยืนยัน",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "ยกเลิก"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    if (this.data.type === 'NEW') {
                        let temp_branchId = this.form.value.branchId.filter(item => item !== '');
                        this.productService.create({
                            barcode: this.form.value.barcode,
                            code: this.form.value.code,
                            name: this.form.value.name,
                            type: this.form.value.type,
                            price: this.form.value.price,
                            cost: this.form.value.cost,
                            image: this.form.value.image,
                            categoryId: this.form.value.categoryId,
                            unit: this.form.value.unit,
                            branchId: temp_branchId,
                            attributes: this.attForm.value.attributes,
                            active: this.form.value.active,
                            remarkStatus: this.form.value.remarkStatus,
                            vatStatus: this.form.value.vatStatus,
                        }).subscribe({
                            error: (err) => {
                                this.toastr.error('ไม่สามารถบันทึกข้อมูลได้')
                            },
                            complete: () => {
                                this.toastr.success('ดำเนินการเพิ่มข้อมูลสำเร็จ')
                                this.dialogRef.close(true)
                            },
                        });
                    }
                }
            }
        )
    }

    update() {
        let formValue = this.form.value
        const confirmation = this.fuseConfirmationService.open({
            title: "ยืนยันการบันทึกข้อมูล",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "primary"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "ยืนยัน",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "ยกเลิก"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    let temp_branchId = this.form.value.branchId.filter(item => item !== '');
                    this.productService.update(this.data.value.id, {
                        barcode: this.form.value.barcode,
                        code: this.form.value.code,
                        name: this.form.value.name,
                        price: this.form.value.price,
                        type: this.form.value.type,
                        cost: this.form.value.cost,
                        image: this.form.value.image,
                        categoryId: this.form.value.categoryId,
                        unit: this.form.value.unit,
                        branchId: temp_branchId,
                        attributes: this.attForm.value.attributes,
                        active: this.form.value.active,
                        remarkStatus: this.form.value.remarkStatus,
                        vatStatus: this.form.value.vatStatus,
                    }).subscribe({
                        next: (resp: any) => {
                            this.toastr.success('ดำเนินการเพิ่มข้อมูลสำเร็จ')
                            this.dialogRef.close(true)
                        },error: () => {
                            this.toastr.error('เกิดข้อผิดพลาด')
                        }
                    })
                }
            }
        )
    }

    uploadSuccess(event): void {
        this.imageUploadService.upload(event).subscribe({
            next: (resp: any) => {
                this.form.patchValue({
                    image: resp.filename
                });
            },
            error: (err) => {
                alert(JSON.stringify(err))
            },
        })
    }

    validateNotEmptyArray(): ValidatorFn {
        return (control: AbstractControl): { [key: string]: any } | null => {
          if (control.value.length === 1 && control.value[0] === '') {
            return { 'invalidBranchSelection': true };
          }
          return null;
        };
    }

    selectionChanged(event: any) {
        // Map selected branches to their IDs
        this.selectedBranches = event.value.filter(item => item !== '');
        if (this.selectedBranches.length == 0){
            this.selectedBranches = ['']
        }
        this.form.patchValue({
            branchId: this.selectedBranches
        });
        console.log('Selected Branch IDs:', this.form.get('branchId')?.value);
    }
}
