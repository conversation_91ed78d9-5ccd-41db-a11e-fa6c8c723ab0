<div class="flex flex-col overflow-y-auto">
    <mat-toolbar class="flex justify-between">
        <span class="example-spacer"></span>

        <div>
            <span class="font-bold text-[20px]">{{title}}</span>
        </div>

        <button mat-icon-button mat-dialog-close class="example-icon " aria-label="Example icon-button with menu icon">
            <mat-icon svgIcon="heroicons_outline:x-mark"></mat-icon>
        </button>
    </mat-toolbar>

    <mat-tab-group>
        <mat-tab label="ข้อมูลพื้นฐาน">
            <div class="flex justify-center">
                <form class="flex flex-col h-fit mt-8 p-8 pb-4 bg-card rounded-2xl w-full"
                    [formGroup]="form">
                    <div class="flex items-center gap-5">
                        <div class="grid w-full grid-cols-2 gap-3">
                            <div class="w-full">
                                <mat-form-field class="flex-auto w-full" subscriptSizing="fixed">
                                    <mat-label>Barcode</mat-label>
                                    <input matInput [formControlName]="'barcode'"
                                        [placeholder]="'ระบุ barcode'">
                                    <mat-error>Required field!</mat-error>
                                </mat-form-field>
                            </div>
                            <!--<div class="w-full">
                                <mat-form-field class="flex-auto w-full" subscriptSizing="fixed">
                                    <mat-label>รหัสสินค้า</mat-label>
                                    <input matInput [formControlName]="'code'" required
                                        [placeholder]="'ระบุรหัสสินค้า'">
                                    <mat-error>Required field!</mat-error>
                                </mat-form-field>
                            </div>-->
                            <div class="w-full">
                                <mat-form-field class="flex-auto w-full" subscriptSizing="fixed">
                                    <mat-label>ชื่อสินค้า</mat-label>
                                    <input matInput [formControlName]="'name'" required
                                        [placeholder]="'ระบุชื่อสินค้า'">
                                    <mat-error>Required field!</mat-error>
                                </mat-form-field>
                            </div>
                            <div class="w-full">
                                <mat-form-field class="flex-auto w-full" subscriptSizing="fixed">
                                    <mat-label>ประเภท</mat-label>
                                    <mat-select [formControlName]="'type'" [placeholder]="'เลือกประเภท'">
                                        <mat-option value="product">product</mat-option>
                                        <mat-option value="special">special</mat-option>
                                    </mat-select>
                                    <mat-error>Required field!</mat-error>
                                </mat-form-field>
                            </div>
                            <div class="w-full">
                                <mat-form-field class="flex-auto w-full " subscriptSizing="fixed">
                                    <mat-label>ราคาสินค้า</mat-label>
                                    <input matInput mask="separator.2" thousandSeparator="," separatorLimit="99999999"
                                        [allowNegativeNumbers]="false" [formControlName]="'price'" required
                                        [placeholder]="'ระบุราคาสินค้า'">
                                    <mat-error>Required field!</mat-error>
                                </mat-form-field>
                            </div>
                            <div class="w-full">
                                <mat-form-field class="flex-auto w-full " subscriptSizing="fixed">
                                    <mat-label>ต้นทุนสินค้า</mat-label>
                                    <input matInput mask="separator.2" thousandSeparator="," separatorLimit="99999999"
                                        [allowNegativeNumbers]="false" [formControlName]="'cost'" required
                                        [placeholder]="'ระบุต้นทุนสินค้า'">
                                    <mat-error>Required field!</mat-error>
                                </mat-form-field>
                            </div>
                            <div class="w-full">
                                <mat-form-field class="flex-auto w-full">
                                    <mat-label>หมวดหมู่</mat-label>
                                    <mat-select [formControlName]="'categoryId'" [placeholder]="'ระบุหมวดหมู่'">
                                        <mat-option *ngFor="let item of catagories; let i = index;"
                                            [value]="item.id">{{item.name}}</mat-option>
                                    </mat-select>
                                    <mat-error>Required field!</mat-error>
                                </mat-form-field>
                            </div>
                            <div class="w-full">
                                <mat-form-field class="flex-auto w-full">
                                    <mat-label>หน่วยนับ</mat-label>
                                    <mat-select [formControlName]="'unit'" [placeholder]="'ระบุหน่วย'">
                                        <mat-option *ngFor="let item of units; let i = index;"
                                            [value]="item.id">{{item.name}}</mat-option>
                                    </mat-select>
                                    <mat-error>Required field!</mat-error>
                                </mat-form-field>
                            </div>
                            <div class="w-full">
                                <mat-form-field class="flex-auto w-full">
                                    <mat-label>สาขา</mat-label>
                                    <mat-select [formControlName]="'branchId'" multiple (selectionChange)="selectionChanged($event)">
                                        <mat-option [disabled]="true" value="">เลือกสาขา</mat-option>
                                        <mat-option *ngFor="let item of branch; let i = index;"
                                            [value]="item.id">{{item.name}}</mat-option>
                                    </mat-select>
                                    <mat-error>กรุณาเลือกสาขาอย่างน้อย 1 สาขา</mat-error>
                                </mat-form-field>
                            </div>
                            <!-- <div *ngIf="delete_toggle" class="relative group" (click)="deleteImgToInsert()">
                                <img [src]="form.value.image" alt="Uploaded Image"
                                    class="opacity-100 group-hover:opacity-50">
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <button
                                        class="px-4 py-2 font-semibold text-white bg-transparent border opacity-0 group-hover:opacity-100 group-hover:bg-red-200 group-hover:text-red-500 border-white-500 group-hover:border-transparent rounded-xl">
                                        ลบรูปภาพ
                                    </button>
                                </div>
                            </div> -->

                        </div>
                        <div class="mb-3">
                            <asha-image-upload [initial]="item?.imageUrl" (uploadSuccess)="uploadSuccess($event)"></asha-image-upload>
                            <!-- <ngx-awesome-uploader [adapter]="adapter" [accept]="'image/*'" [uploadType]="'single'" class="w-full"
                                [fileMaxSize]="200" (uploadSuccess)="uploadSuccess($event)">
                            </ngx-awesome-uploader> -->
                            <div class="ml-2 w-full flex gap-7 items-center mt-15">
                                <mat-slide-toggle formControlName="active" color="primary">แสดง</mat-slide-toggle>
                                <mat-slide-toggle formControlName="vatStatus" color="primary">vat status</mat-slide-toggle>
                                <mat-slide-toggle formControlName="remarkStatus" color="primary">remark status</mat-slide-toggle>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </mat-tab>
    </mat-tab-group>

    <div class="bottom-0 flex justify-between w-full px-8 py-5 mt-2 bg-white border-t dark:bg-gray-700 ">
        <div></div>
        <div mat-dialog-actions class="flex justify-end">
            <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
                ตกลง
            </button>
            <button class="px-6 ml-3" mat-flat-button [color]="'warn'" mat-dialog-close>
                ยกเลิก
            </button>
        </div>
    </div>
</div>
