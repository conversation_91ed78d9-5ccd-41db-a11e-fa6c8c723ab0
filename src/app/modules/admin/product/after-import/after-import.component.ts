import { Subscription } from 'rxjs';
import { Component, OnInit, OnChanges, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataTablesModule } from 'angular-datatables';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import {
    MatDialog,
    MatDialogTitle,
    MatDialogContent,
    MatDialogActions,
    MatDialogClose,
    MatDialogRef,
    MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { FormBuilder, FormControl, FormGroup, FormsModule, Validators } from '@angular/forms';
import { MatToolbarModule } from '@angular/material/toolbar';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { ToastrService } from 'ngx-toastr';
import { MatRadioModule } from '@angular/material/radio';
import { createFileFromBlob } from 'app/modules/shared/helper';;

@Component({
    selector: 'app-user-form',
    standalone: true,
    templateUrl: './after-import.component.html',
    styleUrl: './after-import.component.scss',
    imports: [CommonModule, DataTablesModule, MatIconModule, MatFormFieldModule, MatInputModule,
        FormsModule, MatToolbarModule,
        MatButtonModule,
        MatDialogTitle,
        MatDialogContent,
        MatDialogActions,
        MatDialogClose,
        MatSelectModule,
        ReactiveFormsModule,
        MatInputModule,
        MatFormFieldModule,
        MatRadioModule
    ]
})
export class AfterImportComponent implements OnInit {

    form: FormGroup;
    stores: any[] = [];
    formFieldHelpers: string[] = ['fuse-mat-dense'];

    constructor(
        private dialogRef: MatDialogRef<AfterImportComponent>,
        @Inject(MAT_DIALOG_DATA) public data: any,
        public dialog: MatDialog,
        private FormBuilder: FormBuilder,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,

    ) {
        console.log(' this.form', this.data);
    }

    ngOnInit(): void {
        console.log('Open Dialog AfterImportComponent');
        
    }


    //Submit() {
    //    const confirmation = this.fuseConfirmationService.open({
    //        title: "ยืนยันการบันทึกข้อมูล",
    //        icon: {
    //            show: true,
    //            name: "heroicons_outline:exclamation-triangle",
    //            color: "primary"
    //        },
    //        actions: {
    //            confirm: {
    //                show: true,
    //                label: "ยืนยัน",
    //                color: "primary"
    //            },
    //            cancel: {
    //                show: true,
    //                label: "ยกเลิก"
    //            }
    //        },
    //        dismissible: false
    //    })

    //    confirmation.afterClosed().subscribe(
    //        result => {
    //            if (result == 'confirmed') {

    //                if (this.data.type === 'NEW') {
    //                    const formData = new FormData();
    //                    Object.entries(this.form.value).forEach(
    //                        ([key, value]: any[]) => {
    //                            if (value !== '' && value !== 'null' && value !== null) {
    //                                formData.append(key, value);
    //                            }
    //                        }
    //                    );
    //                    this.bookDailyMealService.import(formData).subscribe({
    //                        error: (err) => {
    //                            this.toastr.error('ไม่สามารถบันทึกข้อมูลได้')
    //                        },
    //                        complete: () => {
    //                            this.toastr.success('ดำเนินการเพิ่มข้อมูลสำเร็จ')
    //                            this.dialogRef.close(true)
    //                        },
    //                    });
    //                } else {
    //                    // comment นานแล้วไม่รู้ทำไม
    //                    // this.bookDailyMealService.update(this.data.value.id ,formValue).subscribe({
    //                    //     error: (err) => {
    //                    //         this.toastr.error('ไม่สามารถบันทึกข้อมูลได้')
    //                    //     },
    //                    //     complete: () => {
    //                    //         this.toastr.success('ดำเนินการแก้ไขข้อมูลสำเร็จ')
    //                    //         this.dialogRef.close(true)
    //                    //     },
    //                    // });
    //                }
    //            }
    //        }
    //    )
    //}

    onClose() {
        console.log('close dialog after import');
        this.dialogRef.close()
    }
}
