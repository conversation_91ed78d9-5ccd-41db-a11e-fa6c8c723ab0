<div class="w-full overflow-auto">
    <div class="w-full flex items-center">
        <h1 mat-dialog-title class="text-xl font-semibold mb-4">UPDATE TABLE</h1>
    </div>
    <div class="flex flex-col items-center" [ngClass]="data.errorDetails.length !== 0? 'mb-[10px]': 'mb-[0px]'">
        <h6 class="text-green-600">CREATE -->  {{data.create}}</h6>
        <h6 class="text-green-600">UPDATE -->  {{data.update}}</h6>
        <h6 class="text-red-500">ERROR&nbsp;&nbsp; -->  {{data.error}}</h6>
    </div>
    <div mat-dialog-content class="overflow-y-auto md:max-h-180">
        <table class="w-full border-collapse border-[1px]">
            <tr *ngIf="data.errorDetails.length !== 0">
                <th class="text-center border border-slate-600">row</th>
                <th class="text-center border border-slate-600">error</th>
            </tr>
            <tr *ngFor="let temp of data.errorDetails">
                <td class="text-center border border-slate-700">{{temp.row}}</td>
                <td class="text-center border border-slate-700">{{temp.error}}</td>
            </tr>
        </table>
    </div>
    <!-- <div> check : {{this.form.value | json}}</div> -->
    <div mat-dialog-actions class="flex justify-center">
        <!--<button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
            ตกลง
        </button>-->
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="onClose()">
            ตกลง
        </button>
    </div>
</div>
