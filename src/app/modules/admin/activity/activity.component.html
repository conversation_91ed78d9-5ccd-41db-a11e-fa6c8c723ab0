<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
    <div class="flex flex-row justify-between pb-2 mt-5 mb-3">
      <div>
        <h2 class="text-2xl md:text-xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
          รายการกิจกรรม
        </h2>
      </div>
      <button mat-flat-button class="bg-[rgb(2,124,57)] mr-4" [ngClass]="data_year && data_month && data_day ? 'opacity-100' : 'opacity-60'" (click)="data_year && data_month && data_day ? exportExcel(): exportFail()" ><!--class="translate-y-[10%]"-->
        <mat-icon class="icon-size-5" svgIcon="heroicons_solid:table-cells"></mat-icon>
        <span class="ml-2 text-white"> Excel</span>
      </button>
      <!-- <button mat-flat-button [color]="'primary'" (click)="opendialogapro()">
        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:plus-circle'"></mat-icon>
        <span class="ml-2"> นำเข้าข้อมูล</span>
      </button> -->
    </div>
    <div class="flex items-center justify-start gap-4">
      <mat-form-field appearance="fill">
        <mat-label>วันที่</mat-label>
        <input matInput [matDatepicker]="picker" [(ngModel)]="datePicked" placeholder="วันที่" (dateChange)="filterdate()">
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
      </mat-form-field>
      <mat-form-field class="w-full md:w-1/4">
        <mat-label>ผู้ใช้งาน</mat-label>
        <mat-select [(ngModel)]="selected_user" (selectionChange)="onChangeUser()">
            <mat-option  [value]="''" >
                ALL
            </mat-option>
            <mat-option *ngFor="let item of filterUser" [value]="item.id" >
                {{item.username}}
            </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    
    <!--<div [formGroup]="formstatus" class="flex flex-row justify-start mb-2">
      <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-1/4">
        <mat-label>สถานะ</mat-label>
        <mat-select [formControlName]="'filter_status'" (selectionChange)="onChangeStatus()">
            <mat-option  [value]="''" >
                ALL
            </mat-option>
            <mat-option *ngFor="let item of filterStatus" [value]="item" >
                {{item}}
            </mat-option>
        </mat-select>
      </mat-form-field>
    </div>-->
    <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="row-border hover w-full">
    </table>
  </div>
</div>

<ng-template #btNg let-data="adtData">
  <button mat-icon-button [color]="'primary'" [matMenuTriggerFor]="menu">
    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cog-8-tooth'"></mat-icon>
  </button>
  <mat-menu #menu="matMenu">
    <button mat-menu-item (click)="openDialogEdit(data)">
      <mat-icon [svgIcon]="'heroicons_solid:eye'"></mat-icon>
      <span>ดูรายละเอียด</span>
    </button>
    <mat-divider></mat-divider>
    <!-- <button mat-menu-item (click)="clickDelete(data.id)">
      <mat-icon [svgIcon]="'heroicons_solid:trash'"></mat-icon>
      <span>ลบ</span>
    </button> -->
  </mat-menu>
</ng-template>
