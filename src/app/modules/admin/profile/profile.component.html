<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto p-4 md:p-5 bg-card m-4  rounded-md">
    <div class="flex flex-row justify-between pb-2">
      <div>
        <h2 class="text-2xl md:text-xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate md:px-6">
          ข้อมูลส่วนตัว
        </h2>
      </div>
    </div>
    <div class="flex-auto p-4 md:p-5">
      <div class="grid gap-4 w-full sm:grid-cols-1 md:grid-cols-1">
        <form [formGroup]="form">
          <div class="flex flex-col my-2">
            <div class="-mx-3 md:flex mb-6">
              <div class="md:w-full px-3">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>code</mat-label>
                  <input matInput [placeholder]="'กรุณากรอกรหัส'" formControlName="code">
                </mat-form-field>
              </div>
              <div class="md:w-full px-3">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>username</mat-label>
                  <input matInput [placeholder]="'กรุณากรอกยูเซอร์เนม'" formControlName="username">
                </mat-form-field>
              </div>
            </div>
            <div class="-mx-3 md:flex mb-6">
              <div class="md:w-full px-3">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>ชื่อ-นามสกุล</mat-label>
                  <input matInput [placeholder]="'กรอกหน่วยนับ'" formControlName="fullName">
                </mat-form-field>
              </div>
              <div class="md:w-full px-3">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>phone</mat-label>
                  <input matInput [placeholder]="'กรุณากรอกเบอร์โทร'" formControlName="phoneNumber">
                </mat-form-field>
              </div>
            </div>
            <div mat-dialog-actions class="flex justify-end mt-2">
              <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit('personal')">
                ตกลง
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div class="flex flex-row justify-between pb-2">
      <div>
        <h2 class="text-2xl md:text-xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate md:px-6">
          เปลี่ยนรหัสผ่าน
        </h2>
      </div>
    </div>
    <div class="flex-auto p-4 md:p-5">
      <div class="grid gap-4 w-full sm:grid-cols-1 md:grid-cols-1">
        <form [formGroup]="formpassword">
          <div class="flex flex-col my-2">
            <div class="-mx-3 md:flex mb-6">
              <div class="md:w-full px-3">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label class="pb-1">รหัสผ่านปัจจุบัน</mat-label>
                  <input id="password" matInput [placeholder]="'กรุณากรอกรหัสผ่านปัจจุบัน'" type="password" [formControlName]="'oldPassword'" #passwordoldField required>

                  <button mat-icon-button type="button"
                      (click)="passwordoldField.type === 'password' ? passwordoldField.type = 'text' : passwordoldField.type = 'password'"
                      matSuffix>
                      <mat-icon class="icon-size-5" *ngIf="passwordoldField.type === 'password'"
                          [svgIcon]="'heroicons_solid:eye'"></mat-icon>
                      <mat-icon class="icon-size-5" *ngIf="passwordoldField.type === 'text'"
                          [svgIcon]="'heroicons_solid:eye-slash'"></mat-icon>
                  </button>
                </mat-form-field>
              </div>
              <div class="md:w-full px-3">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label class="pb-1">รหัสผ่านใหม่</mat-label>
                  <input id="password" matInput [placeholder]="'กรุณากรอกรหัสผ่านใหม่'" type="password" [formControlName]="'newPassword'" #passwordnewField required pattern="^(?=.*[A-Z])(?=.*[0-9])(?=.*[-+_!@#$%^&*,.?])(?=.*[a-z]).{8,}$">

                  <button mat-icon-button type="button"
                      (click)="passwordnewField.type === 'password' ? passwordnewField.type = 'text' : passwordnewField.type = 'password'"
                      matSuffix>
                      <mat-icon class="icon-size-5" *ngIf="passwordnewField.type === 'password'"
                          [svgIcon]="'heroicons_solid:eye'"></mat-icon>
                      <mat-icon class="icon-size-5" *ngIf="passwordnewField.type === 'text'"
                          [svgIcon]="'heroicons_solid:eye-slash'"></mat-icon>
                  </button>
                  <mat-error class="text-xs">Password must be a combination of lower-case, upper-case, numbers, symbols and at least 8 characters long
                  </mat-error>
                </mat-form-field>
              </div>
              <div class="md:w-full">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label class="pb-1">ยืนยันรหัสผ่าน</mat-label>
                  <input id="password" matInput [placeholder]="'ยืนยันรหัสผ่านอีกครั้ง'" type="password" [formControlName]="'conPassword'" #passwordconField required pattern="^(?=.*[A-Z])(?=.*[0-9])(?=.*[-+_!@#$%^&*,.?])(?=.*[a-z]).{8,}$">

                  <button mat-icon-button type="button"
                      (click)="passwordconField.type === 'password' ? passwordconField.type = 'text' : passwordconField.type = 'password'"
                      matSuffix>
                      <mat-icon class="icon-size-5" *ngIf="passwordconField.type === 'password'"
                          [svgIcon]="'heroicons_solid:eye'"></mat-icon>
                      <mat-icon class="icon-size-5" *ngIf="passwordconField.type === 'text'"
                          [svgIcon]="'heroicons_solid:eye-slash'"></mat-icon>
                  </button>
                  <mat-error class="text-xs">Password must be a combination of lower-case, upper-case, numbers, symbols and at least 8 characters long
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
            <div mat-dialog-actions class="flex justify-end mt-2">
              <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit('password')">
                ตกลง
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<!--<app-page-branch [storeId]="storeId"></app-page-branch>-->
