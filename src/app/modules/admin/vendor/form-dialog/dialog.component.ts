import { Subscription } from 'rxjs';
import { Component, OnInit, OnChanges, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataTablesModule } from 'angular-datatables';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import {
    MatDialog,
    MatDialogTitle,
    MatDialogContent,
    MatDialogActions,
    MatDialogClose,
    MatDialogRef,
    MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { AbstractControl, FormBuilder, FormControl, FormGroup, FormsModule, ValidatorFn, Validators } from '@angular/forms';
import { MatToolbarModule } from '@angular/material/toolbar';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { UserService } from '../vendor.service';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { ToastrService } from 'ngx-toastr';
import { MatRadioModule } from '@angular/material/radio';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';

@Component({
    selector: 'app-user-form',
    standalone: true,
    templateUrl: './dialog.component.html',
    styleUrl: './dialog.component.scss',
    imports: [CommonModule, DataTablesModule, MatIconModule, MatFormFieldModule, MatInputModule,
        FormsModule, MatToolbarModule,
        MatButtonModule,
        MatDialogTitle,
        MatDialogContent,
        MatDialogActions,
        MatDialogClose,
        MatSelectModule,
        ReactiveFormsModule,
        MatInputModule,
        MatFormFieldModule,
        MatRadioModule,
        MatSlideToggleModule,
    ]
})
export class DialogForm implements OnInit {
    passwordField: any
    form: FormGroup;
    stores: any[] = [];
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    dtOptions: DataTables.Settings = {};
    addForm: FormGroup;
    branches: any[] = [];
    selectedBranches: any[] = [];
    roles: any[] = [];
    //    { id: 2, name: 'Admin'},
    //    { id: 3, name: 'Supervisor'},
    //    { id: 5, name: 'Manager '},
    //    { id: 4, name: 'Cashier'},
    //    { id: 6, name: 'Office'},
    //    { id: 7, name: 'Finance'}
    // ];
    registerForm = new FormGroup({
        password: new FormControl('', [Validators.required, Validators.pattern('^(?=.*[A-Z])(?=.*[0-9])(?=.*[-+_!@#$%^&*,.?])(?=.*[a-z]).{8,}$')]),
    });


    constructor(
        private dialogRef: MatDialogRef<DialogForm>,
        @Inject(MAT_DIALOG_DATA) public data: any,
        public dialog: MatDialog,
        private FormBuilder: FormBuilder,
        public _service: UserService,
        private fuseConfirmationService: FuseConfirmationService,
        private userService: UserService,
        private toastr: ToastrService,
    ) {
        console.log(' this.form', this.data); // roles
      
        if (this.data.type === 'CHANGE') {
      
        }
        else if (this.data.type === 'EDIT') {
            this.form = this.FormBuilder.group({
                code: [this.data.value.code ?? '', [Validators.required]],
                name: [this.data.value.name ?? '', [Validators.required]],
                phone: [this.data.value.phone ?? '', [Validators.required]],
                address: [this.data.value.address ?? '', [Validators.required]],

            });
        } else {
            this.form = this.FormBuilder.group({
                code: ['', [Validators.required]],
                name: ['', [Validators.required]],
                phone: ['', [Validators.required]],
                address: ['', [Validators.required]],
            });
        }
        // console.log('1111',this.data?.type);
    }


    ngOnInit(): void {
        console.log('this.passwordField', this.passwordField);

        this.userService.getBranch().subscribe(() => {
            this.userService.branches$.subscribe((branches) => {
                this.branches = branches;
            });
        });
        if (this.data.type === 'EDIT') {
            //   this.form.patchValue({
            //     ...this.data.value,
            //     roleId: +this.data.value?.role?.id
            //   })

        } else {
            console.log('New');
        }
    }


    Submit() {
        let formValue = this.form.value
        const confirmation = this.fuseConfirmationService.open({
            title: "ยืนยันการบันทึกข้อมูล",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "primary"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "ยืนยัน",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "ยกเลิก"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    if (this.data.type === 'CHANGE') {
                        console.log('');

                    } else if (this.data.type === 'NEW') {
                        this.userService.create(formValue).subscribe({
                            error: (err) => {
                                this.toastr.error('ไม่สามารถบันทึกข้อมูลได้')
                            },
                            complete: () => {
                                this.toastr.success('ดำเนินการเพิ่มข้อมูลสำเร็จ')
                                this.dialogRef.close(true)
                            },
                        });
                    } else {
                        this.userService.update(this.data.value.id, formValue).subscribe({
                            error: (err) => {
                                this.toastr.error('ไม่สามารถบันทึกข้อมูลได้')
                            },
                            complete: () => {
                                this.toastr.success('ดำเนินการแก้ไขข้อมูลสำเร็จ')
                                this.dialogRef.close(true)
                            },
                        });
                    }
                }
            }
        )
    }

    onClose() {
        this.dialogRef.close()
    }

    validateNotEmptyArray(): ValidatorFn {
        return (control: AbstractControl): { [key: string]: any } | null => {
            if (control.value.length === 1 && control.value[0] === '') {
                return { 'invalidBranchSelection': true };
            }
            return null;
        };
    }

    selectionChanged(event: any) {
        // Map selected branches to their IDs
        this.selectedBranches = event.value.filter(item => item !== '');
        if (this.selectedBranches.length == 0) {
            this.selectedBranches = ['']
        }
        this.form.patchValue({
            branchIds: this.selectedBranches
        });
        console.log('Selected Branch IDs:', this.form.get('branchIds')?.value);
    }
}
