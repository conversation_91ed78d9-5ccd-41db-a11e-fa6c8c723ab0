<div class="md:max-w-lg">
    <ng-container *ngIf="this.data?.type !== 'CHANGE'">
        <h1 mat-dialog-title class="mb-4 text-xl font-semibold" *ngIf="this.data?.type === 'NEW'">เพิ่มข้อมูล</h1>
        <h1 mat-dialog-title class="mb-4 text-xl font-semibold" *ngIf="this.data?.type === 'EDIT'">แก้ไขข้อมูล</h1>
        <div mat-dialog-content class="h-fit">
            <form [formGroup]="form">
                <div class="flex-auto">
                    <div class="flex flex-col mb-2 md:flex">
                        <div class="md:w-full mb-1">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                <mat-label>รหัสพนักงาน</mat-label>
                                <input matInput [placeholder]="'รหัสผู้ขาย'" formControlName="code"
                                    [readonly]="this.data.type === 'EDIT'">
                                <mat-error class="text-xs">กรุณาระบุรหัสพนักงาน</mat-error>
                            </mat-form-field>
                        </div>
                        <div class="md:w-full mb-1">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                <mat-label>ชื่อผู้ขาย</mat-label>
                                <input matInput [placeholder]="'กรุณาระบุชื่อผู้ขาย'" formControlName="name">
                                <mat-error class="text-xs">กรุณาระบุชื่อพนักงาน</mat-error>
                            </mat-form-field>
                        </div>
                        <div class="md:w-full mb-1">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                <mat-label>เบอร์ติดต่อ</mat-label>
                                <input matInput [placeholder]="'กรุณาระบุเบอร์ติดต่อ'" formControlName="phone"
                                    type="tel">
                                <mat-error class="text-xs">กรุณาระบุเบอร์ติดต่อ</mat-error>
                            </mat-form-field>
                        </div>
                        <div class="md:w-full mb-1">
                            <mat-form-field appearance="fill" class="w-full" [ngClass]="formFieldHelpers">
                                <mat-label>ที่อยู่</mat-label>
                                <textarea matInput formControlName="address" rows="4"
                                    placeholder="กรอกที่อยู่"></textarea>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <!-- <div> check : {{this.form.value | json}}</div> -->
        <div mat-dialog-actions class="flex justify-end">
            <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
                ตกลง
            </button>
            <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
                ยกเลิก
            </button>
        </div>
    </ng-container>
</div>