import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { APP_INITIALIZER, ApplicationConfig, LOCALE_ID, inject } from '@angular/core';
import { LuxonDateAdapter } from '@angular/material-luxon-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { provideAnimations } from '@angular/platform-browser/animations';
import { PreloadAllModules, provideRouter, withInMemoryScrolling, withPreloading } from '@angular/router';
import { provideFuse } from '@fuse';
import { provideTransloco, TranslocoService } from '@ngneat/transloco';
import { firstValueFrom } from 'rxjs';
import { appRoutes } from 'app/app.routes';
import { provideAuth } from 'app/core/auth/auth.provider';
import { provideIcons } from 'app/core/icons/icons.provider';
import { mockApiServices } from 'app/mock-api';
import { TranslocoHttpLoader } from './core/transloco/transloco.http-loader';
import { provideEnvironmentNgxMask } from 'ngx-mask';
import { provideToastr } from 'ngx-toastr';
import { baseUrlInterceptor } from './base-url.interceptor';
import { BuddhistEraLuxonDateAdapter, MAT_LUXON_DATE_FORMATS } from './buddhist-era-luxon-date-adapter';
// import localeTh from '@angular/common/locales/th';
// import { registerLocaleData } from '@angular/common';

// registerLocaleData(localeTh);

export const appConfig: ApplicationConfig = {
    providers: [
        provideAnimations(),
        provideHttpClient(
            withInterceptors([baseUrlInterceptor])
        ),
        provideEnvironmentNgxMask(),
        provideToastr(),
        provideRouter(appRoutes,
            withPreloading(PreloadAllModules),
            withInMemoryScrolling({ scrollPositionRestoration: 'enabled' }),
        ),

        // Material Date Adapter
        // { provide: MAT_DATE_LOCALE, useValue: 'th-TH' },
        // { provide: LOCALE_ID, useValue: "th-TH" },
        // {
        //     provide: DateAdapter,
        //     useClass: LuxonDateAdapter,
        // },
        // {
        //     provide: MAT_DATE_FORMATS,
        //     useValue: {
        //         parse: {
        //             dateInput: 'D',
        //         },
        //         display: {
        //             dateInput: 'DD',
        //             monthYearLabel: 'LLL yyyy',
        //             dateA11yLabel: 'DD',
        //             monthYearA11yLabel: 'LLLL yyyy',
        //         },
        //     },
        // },

        //Custome Date Adapter
        {
            provide: DateAdapter,
            useClass: BuddhistEraLuxonDateAdapter,
        },
        {
            provide: MAT_DATE_FORMATS,
            useValue: MAT_LUXON_DATE_FORMATS
        },
        // ตั้งค่า locale เป็น 'th-TH' เพื่อให้ Luxon ใช้ภาษาไทยและปฏิทินที่เหมาะสม
        { provide: MAT_DATE_LOCALE, useValue: 'th-TH' },

        // Transloco Config
        provideTransloco({
            config: {
                availableLangs: [
                    {
                        id: 'th',
                        label: 'ไทย',
                    },
                    {
                        id: 'en',
                        label: 'English',
                    },
                ],
                defaultLang: 'th',
                fallbackLang: 'th',
                reRenderOnLangChange: true,
                prodMode: true,
            },
            loader: TranslocoHttpLoader,
        }),
        {
            // Preload the default language before the app starts to prevent empty/jumping content
            provide: APP_INITIALIZER,
            useFactory: () => {
                const translocoService = inject(TranslocoService);
                const defaultLang = translocoService.getDefaultLang();
                translocoService.setActiveLang(defaultLang);

                return () => firstValueFrom(translocoService.load(defaultLang));
            },
            multi: true,
        },

        // Fuse
        provideAuth(),
        provideIcons(),
        provideFuse({
            mockApi: {
                delay: 0,
                services: mockApiServices,
            },
            fuse: {
                layout: 'classic',
                scheme: 'light',
                screens: {
                    sm: '600px',
                    md: '960px',
                    lg: '1280px',
                    xl: '1440px',
                },
                theme: 'theme-default',
                themes: [
                    {
                        id: 'theme-default',
                        name: 'Default',
                    },
                    {
                        id: 'theme-brand',
                        name: 'Brand',
                    },
                    {
                        id: 'theme-teal',
                        name: 'Teal',
                    },
                    {
                        id: 'theme-rose',
                        name: 'Rose',
                    },
                    {
                        id: 'theme-purple',
                        name: 'Purple',
                    },
                    {
                        id: 'theme-amber',
                        name: 'Amber',
                    },
                ],
            },
        }),

    ],
};
