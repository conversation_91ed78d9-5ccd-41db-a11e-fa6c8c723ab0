/* eslint-disable */
import { FuseNavigationItem } from '@fuse/components/navigation';
import { AuthService } from 'app/core/auth/auth.service';

export const defaultNavigation: FuseNavigationItem[] = [
    {
        id: 'admin.dashboard',
        //title: 'ข้อมูลแดชบอร์ด',
        title: 'Dashboard',
        type: 'basic',
        icon: 'heroicons_outline:clipboard-document-check',
        link: '/dashboard',
        hidden: (item) => {
            var role = sessionStorage.getItem('role')

            if (role == 'super_admin') {
                return false
            }
            else if (role == 'admin') {
                return false
            }
            else if (role == 'supervisor') {
                return false
            }
            else {
                return false
            }


        }
    },

    //{
    //    id: 'credit',
    //    title: 'จัดการข้อมูลเครดิต',
    //    type: 'group',
    //    icon: 'heroicons_outline:home',
    //    hidden: (item) => {


    //        var role = sessionStorage.getItem('role')

    //        if (role == 'super_admin') {
    //            return false
    //        }
    //        else if (role == 'manager') {
    //            return false
    //        }

    //        else {
    //            return false
    //        }


    //    },
    //    children: [
    //        {
    //            id: 'credit',
    //            title: 'นำข้อมูลเครดิต',
    //            type: 'basic',
    //            icon: 'heroicons_outline:credit-card',
    //            link: '/credit'
    //        },
    //    ],
    //},
    {
        id: 'receipt',
        title: 'รายการรับเข้า - เบิกออก',
        type: 'group',
        icon: 'heroicons_outline:home',
        hidden: (item) => {
            var role = sessionStorage.getItem('role')

            if (role == 'super_admin') {
                return false
            }
            else if (role == 'admin') {
                return false
            }
            else if (role == 'supervisor') {
                return false
            }
            else {
                return false
            }
        },
        children: [
            {
                id: 'receipt-list',
                title: 'รายการรับเข้า',
                type: 'basic',
                icon: 'heroicons_outline:clipboard-document-check',
                link: '/receipt',
                hidden: (item) => {


                    var role = sessionStorage.getItem('role')

                    if (role == 'super_admin') {
                        return false
                    }
                    else if (role == 'admin') {
                        return false
                    }
                    else if (role == 'supervisor') {
                        return false
                    }
                    else {
                        return false
                    }
                }
            },
            {
                id: 'receipt-list',
                title: 'รายการเบิกออก',
                type: 'basic',
                icon: 'heroicons_outline:clipboard-document-check',
                link: '/receipt',
                hidden: (item) => {


                    var role = sessionStorage.getItem('role')

                    if (role == 'super_admin') {
                        return false
                    }
                    else if (role == 'admin') {
                        return false
                    }
                    else if (role == 'supervisor') {
                        return false
                    }
                    else {
                        return true
                    }
                }
            },
        ],
    },
    {
        id: 'order',
        title: 'จัดการรายการยอดขาย',
        type: 'group',
        icon: 'heroicons_outline:home',
        hidden: (item) => {


            var role = sessionStorage.getItem('role')

            if (role == 'super_admin') {
                return false
            }
            else if (role == 'admin') {
                return false
            }
            else if (role == 'supervisor') {
                return false
            }
            else {
                return false
            }


        },
        children: [
            {
                id: 'order-list',
                title: 'รายการยอดขาย',
                type: 'basic',
                icon: 'heroicons_outline:banknotes',
                link: '/order',
                hidden: (item) => {


                    var role = sessionStorage.getItem('role')

                    if (role == 'super_admin') {
                        return false
                    }
                    else if (role == 'admin') {
                        return false
                    }
                    else if (role == 'supervisor') {
                        return false
                    }
                    else {
                        return false
                    }
                }
            },
        ],
    },
    {
        id: 'report',
        title: 'รายงาน',
        subtitle: 'ข้อมูลเกี่ยวกับรายงาน',
        type: 'group',
        icon: 'heroicons_outline:home',
        hidden: (item) => {


            var role = sessionStorage.getItem('role')
            if (role == 'super_admin') {
                return false
            }
            else if (role == 'admin') {
                return false
            }
            else if (role == 'supervisor') {
                return false
            }
            else {
                return false
            }


        },
        children: [
            // {
            //     id: 'report.tap-card-daily',
            //     title: 'รายงานแตะบัตรประจำวัน',
            //     type: 'basic',
            //     icon: 'heroicons_outline:document-chart-bar',
            //     link: '/report/card'
            // },
            // {
            //     id: 'report.tap-card-monthly',
            //     title: 'รายงานแตะบัตร',
            //     type: 'basic',
            //     icon: 'heroicons_outline:document-chart-bar',
            //     link: '/report/card'
            // },
            //{
            //    id: 'report.tap-card',
            //    title: 'รายงาน',
            //    type: 'basic',
            //    icon: 'heroicons_outline:document-chart-bar',
            //    link: '/report/card'
            //},
            {
                id: 'report.tap-list',
                title: 'รายงาน',
                type: 'basic',
                icon: 'heroicons_outline:document-chart-bar',
                link: '/report/list'
            },
        ],
    },
    {
        id: 'admin',
        title: 'จัดการข้อมูลสินค้า',
        subtitle: 'ขัอมูลเกี่ยวกับสินค้า',
        type: 'group',
        icon: 'heroicons_outline:home',
        hidden: (item) => {


            var role = sessionStorage.getItem('role')

            if (role == 'super_admin') {
                return false
            }
            else if (role == 'admin') {
                return false
            }
            else if (role == 'supervisor') {
                return false
            }

            else {
                return false
            }


        },
        children: [
            // {
            //     id: 'admin.category',
            //     title: 'หน้าจอสินค้า',
            //     type: 'basic',
            //     icon: 'heroicons_solid:computer-desktop',
            //     link: '/panel'
            // },
            {
                id: 'admin.category',
                title: 'ประเภทสินค้า',
                type: 'basic',
                icon: 'heroicons_solid:circle-stack',
                link: '/category',
                hidden: (item) => {


                    var role = sessionStorage.getItem('role')

                    if (role == 'super_admin') {
                        return false
                    }
                    else if (role == 'admin') {
                        return false
                    }
                    else {
                        return false
                    }
                }
            },
            {
                id: 'unit',
                title: 'หน่วยนับ',
                type: 'basic',
                icon: 'heroicons_solid:inbox-stack',
                link: '/unit',
                hidden: (item) => {


                    var role = sessionStorage.getItem('role')

                    if (role == 'super_admin') {
                        return false
                    }
                    else if (role == 'admin') {
                        return false
                    }
                    else {
                        return false
                    }
                }
            },
            {
                id: 'AddProduct',
                title: 'สินค้า',
                type: 'basic',
                icon: 'heroicons_solid:squares-plus',
                link: '/product',
                hidden: (item) => {


                    var role = sessionStorage.getItem('role')

                    if (role == 'super_admin') {
                        return false
                    }
                    else if (role == 'admin') {
                        return false
                    }
                    else if (role == 'supervisor') {
                        return false
                    }
                    else {
                        return false
                    }
                }
            },
            {
                id: 'inventory',
                title: 'สต็อก',
                type: 'basic',
                icon: 'heroicons_solid:beaker',
                link: '/inventory',
                hidden: (item) => {
                    var role = sessionStorage.getItem('role')

                    if (role == 'super_admin') {
                        return false
                    }
                    else if (role == 'admin') {
                        return false
                    }
                    else if (role == 'supervisor') {
                        return false
                    }
                    else {
                        return false
                    }
                }
            },
            {
                id: 'mix-match-rule',
                title: 'เงื่อนไขการขาย',
                type: 'basic',
                icon: 'heroicons_solid:rocket-launch',
                link: '/mix-match-rule',
                hidden: (item) => {
                    var role = sessionStorage.getItem('role')

                    if (role == 'super_admin') {
                        return false
                    }
                    else if (role == 'admin') {
                        return false
                    }
                    else {
                        return false
                    }
                }
            },
        ],
    },

    {
        id: 'panel-group',
        title: 'จัดการหน้าจอขาย',
        type: 'group',
        icon: 'heroicons_outline:home',
        hidden: (item) => {


            var role = sessionStorage.getItem('role')

            if (role == 'super_admin') {
                return false
            }
            else if (role == 'admin') {
                return false
            }

            else {
                return false
            }


        },
        children: [
            {
                id: 'panel',
                title: 'รายการหน้าจอขาย',
                type: 'basic',
                icon: 'heroicons_outline:computer-desktop',
                link: '/panel',
                hidden: (item) => {


                    var role = sessionStorage.getItem('role')

                    if (role == 'super_admin') {
                        return false
                    }
                    else if (role == 'admin') {
                        return false
                    }

                    else {
                        return false
                    }


                }
            },
        ],
    },
    {
        id: 'admin',
        title: 'จัดการระบบ',
        subtitle: 'ขัอมูลเกี่ยวกับระบบ',
        type: 'group',
        icon: 'heroicons_outline:home',
        hidden: (item) => {
            var role = sessionStorage.getItem('role')

            if (role == 'super_admin') {
                return false
            }
            else if (role == 'admin') {
                return false
            }
            else if (role == 'supervisor') {
                return false
            }
            else {
                return false
            }
        },
        children: [
            {
                id: 'admin.store',
                title: 'ข้อมูลร้านค้า',
                type: 'basic',
                icon: 'heroicons_outline:building-office-2',
                link: '/store/1',
                hidden: (item) => {
                    var role = sessionStorage.getItem('role')

                    if (role == 'super_admin') {
                        return false
                    }
                    else {
                        return false
                    }


                }

            },
            // {
            //     id: 'admin.department',
            //     title: 'แผนกงาน',
            //     type: 'basic',
            //     icon: 'heroicons_outline:list-bullet',
            //     link: '/admin/department/list',
            // },
            {
                id: 'user',
                title: 'ผู้ใช้งาน',
                type: 'basic',
                icon: 'heroicons_outline:user-group',
                link: '/user',
                hidden: (item) => {


                    var role = sessionStorage.getItem('role')

                    if (role == 'super_admin') {
                        return false
                    }
                    else {
                        return false
                    }


                }
            },
            // {
            //     id: 'admin.permission',
            //     title: 'สิทธิ์การใช้งาน',
            //     type: 'basic',
            //     icon: 'heroicons_outline:key',
            //     link: '/admin/permission/list',
            // },
            //{
            //    id: 'promotion',
            //    title: 'โปรโมชั่น',
            //    type: 'basic',
            //    icon: 'heroicons_mini:archive-box',
            //    link: '/promotion'
            //},
            // {
            //     id: 'member',
            //     title: 'สมาชิก',
            //     type: 'basic',
            //     icon: 'heroicons_solid:rectangle-stack',
            //     link: '/member',
            //     hidden: (item) => {


            //         var role = sessionStorage.getItem('role')

            //         if (role == 'super_admin') {
            //             return false
            //         }
            //         else if (role == 'admin'){
            //             return false
            //         }
            //         else if (role == 'supervisor') {
            //             return false
            //         }
            //         else {
            //             return false
            //         }


            //     }
            // },
            {
                id: 'banner',
                title: 'แบนเนอร์',
                type: 'basic',
                icon: 'heroicons_solid:photo',
                link: '/banner',
                hidden: (item) => {


                    var role = sessionStorage.getItem('role')

                    if (role == 'super_admin') {
                        return false
                    }

                    else {
                        return false
                    }


                }
            },
            {
                id: 'shift',
                title: 'กะทำงาน',
                type: 'basic',
                icon: 'heroicons_solid:clock',
                link: '/shift',
                hidden: (item) => {

                    var role = sessionStorage.getItem('role')

                    if (role == 'super_admin') {
                        return false
                    }
                    else if (role == 'admin') {
                        return false
                    }
                    else if (role == 'supervisor') {
                        return false
                    }
                    else {
                        return false
                    }

                }
            },
            {
                id: 'device',
                title: 'อุปกรณ์',
                type: 'basic',
                icon: 'heroicons_solid:device-tablet',
                link: '/device',
                hidden: (item) => {


                    var role = sessionStorage.getItem('role')

                    if (role == 'super_admin') {
                        return false
                    }
                    else if (role == 'admin') {
                        return false
                    }
                    else {
                        return false
                    }


                }
            },
            {
                id: 'vendor',
                title: 'ผู้ขาย',
                type: 'basic',
                icon: 'heroicons_solid:building-storefront',
                link: '/vendor',
                hidden: (item) => {


                    var role = sessionStorage.getItem('role')

                    if (role == 'super_admin') {
                        return false
                    }
                    else if (role == 'admin') {
                        return false
                    }
                    else {
                        return false
                    }


                }
            },
        ],
    },
    {
        id: 'activity-log',
        title: 'Activity log',
        type: 'group',
        icon: 'heroicons_outline:home',
        hidden: (item) => {


            var role = sessionStorage.getItem('role')

            if (role == 'super_admin') {
                return false
            }
            else {
                return false
            }


        },
        children: [
            {
                id: 'activity',
                title: 'รายการกิจกรรม',
                type: 'basic',
                icon: 'heroicons_outline:presentation-chart-line',
                link: '/activity',
                hidden: (item) => {

                    var role = sessionStorage.getItem('role')

                    if (role == 'super_admin') {
                        return false
                    }
                    else {
                        return false
                    }
                }
            },
        ],
    },
    {
        id: 'self',
        title: 'ส่วนตัว',
        subtitle: 'จัดการโปรไฟล์',
        type: 'group',
        icon: 'heroicons_outline:home',
        children: [
            {
                id: 'self.employee',
                title: 'แก้ไขข้อมูลส่วนตัว',
                type: 'basic',
                icon: 'heroicons_outline:user',
                link: '/profile',
            },
            {
                id: 'admin.logout',
                title: 'ออกจากระบบ',
                type: 'basic',
                icon: 'heroicons_outline:arrow-left-on-rectangle',
                link: '/sign-out',
            },
        ],
    },
    {
        id: 'version',
        title: 'V 1.0.0',
        type: 'group'
    }
];
export const compactNavigation: FuseNavigationItem[] = [
    {
        id: 'example',
        title: 'Example',
        type: 'basic',
        icon: 'heroicons_outline:chart-pie',
        link: '/example'
    }
];
export const futuristicNavigation: FuseNavigationItem[] = [
    {
        id: 'example',
        title: 'Example',
        type: 'basic',
        icon: 'heroicons_outline:chart-pie',
        link: '/example'
    }
];
export const horizontalNavigation: FuseNavigationItem[] = [
    {
        id: 'example',
        title: 'Example',
        type: 'basic',
        icon: 'heroicons_outline:chart-pie',
        link: '/example'
    }
];
