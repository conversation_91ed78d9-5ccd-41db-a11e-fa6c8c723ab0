name: Build CIS Web

on:
  push:
    branches: [ "cis" ]

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: 18
        cache: 'npm'

    - name: Install dependencies
      run: npm install

    - name: Build CIS Web
      run: npm run build:cis

    - name: rsync deployments
      uses: burnett01/rsync-deployments@7.0.1
      with:
        switches: -rltvz --delete --omit-dir-times
        path: dist/fuse/
        remote_path: /var/www/cis/
        remote_host: ${{ secrets.DEPLOY_HOST }}
        remote_user: ${{ secrets.DEPLOY_USER }}
        remote_key: ${{ secrets.DEPLOY_KEY }}
