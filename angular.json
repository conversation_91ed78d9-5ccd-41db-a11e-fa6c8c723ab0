{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"fuse": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "asha", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/fuse", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "allowedCommonJsDependencies": ["apexcharts", "highlight.js", "crypto-js/enc-utf8", "crypto-js/hmac-sha256", "crypto-js/enc-base64", "flat", "quill"], "assets": ["src/favicon-16x16.png", "src/favicon-32x32.png", "src/assets", {"glob": "_redirects", "input": "src", "output": "/"}], "stylePreprocessorOptions": {"includePaths": ["src/@fuse/styles"]}, "styles": ["src/@fuse/styles/tailwind.scss", "src/@fuse/styles/themes.scss", "src/styles/vendors.scss", "src/@fuse/styles/main.scss", "src/styles/styles.scss", "src/styles/tailwind.scss", "node_modules/datatables.net-dt/css/jquery.dataTables.min.css", "node_modules/datatables.net-select-dt/css/select.dataTables.css", "node_modules/ngx-toastr/toastr.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/datatables.net/js/jquery.dataTables.min.js", "node_modules/datatables.net-select/js/dataTables.select.js"]}, "configurations": {"uat": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "75kb", "maximumError": "90kb"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}, "production": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "75kb", "maximumError": "90kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.production.ts"}]}, "ews": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "75kb", "maximumError": "90kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.ews.ts"}]}, "essilor": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "75kb", "maximumError": "90kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.essilor.ts"}]}, "sct": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "75kb", "maximumError": "90kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.sct.ts"}]}, "bih": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "75kb", "maximumError": "90kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.bih.ts"}]}, "cis": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "75kb", "maximumError": "90kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.cis.ts"}]}, "cisprod": {"optimization": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.cisprod.ts"}]}}, "defaultConfiguration": "uat"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "fuse:build:production"}, "development": {"buildTarget": "fuse:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "fuse:build"}}, "test": {"builder": "@angular-builders/jest:run", "options": {"configPath": "./jest.config.ts"}}}}}, "cli": {"analytics": "79c7dd5e-55d9-4aa4-b7e4-4742cf8d61e9"}}